# Java测试质量分析器 (Java Test Quality Analyzer)

一个基于Python Flask的Web应用程序，用于评估Java单元测试的质量和有效性。该工具类似于SonarQube，专注于根据10个核心原则对Java单元测试进行静态分析和评分。

## 核心评估原则

1. **独立性原则**：每个单元测试应该是独立的，不依赖于其他测试的执行结果或顺序
2. **自动化原则**：测试应能自动运行，无需人工干预或手动步骤
3. **测试粒度**：测试应足够小且聚焦，有助于精确定位问题
4. **边界值测试**：测试应覆盖边缘情况，如最大值、最小值、临界条件等
5. **正确输入测试**：测试应验证系统能正确处理预期的正常输入数据
6. **错误处理测试**：测试应验证系统对异常情况、错误输入的处理能力
7. **目录结构规范**：应遵循标准的测试目录结构，保持源码与测试代码的清晰对应
8. **命名规范**：测试类应以被测试类名+Test命名，方法名应清晰表达测试意图
9. **断言原则**：每个断言应只验证一件事，使用恰当的断言类型和明确的错误消息
10. **核心代码原则**：应优先覆盖项目中的关键逻辑路径和核心功能

## 技术栈

- 后端：Flask
- 数据库：SQLite（通过SQLAlchemy）
- 前端：HTML、CSS、JavaScript、Jinja2模板、Bootstrap 5、Chart.js
- 测试解析：Tree-sitter、Pygments
- AI分析：OpenRouter/通义千问大语言模型

## 项目特点

1. **基于AI的智能分析**：利用先进的大语言模型进行测试代码深度语义分析
2. **多维度评分系统**：根据10条原则进行全面评估，生成详细得分
3. **可视化报告**：使用Chart.js生成雷达图等直观评分展示
4. **改进建议**：提供针对性的测试改进建议和代码示例
5. **项目历史追踪**：记录项目测试质量的改进历程
6. **友好的用户界面**：现代化UI设计，操作简单明了

## 主要功能

- 上传Java源代码和测试文件（支持单文件或ZIP项目包）
- 分析测试是否符合10条核心规则
- 识别潜在问题并提供建议
- 提供综合评分和详细分析报告
- 存储历史分析记录
- 生成优化后的测试代码示例

## 安装与运行

1. 克隆仓库：
```
git clone <repository-url>
cd java_test_quality_analyzer
```

2. 安装依赖：
```
pip install -r requirements.txt
```

3. 配置环境变量：
```
cp .env.example .env
```
然后编辑.env文件，添加你的OpenRouter API密钥。
**注意**：请在OpenRouter平台(https://openrouter.ai/)获取API密钥，格式为`sk-or-v1-xxxxxxxxx`。

4. 运行应用：
```
python app.py
```

5. 访问：
浏览器打开 http://localhost:5000

## 使用说明

1. 创建新项目：点击"新建项目"按钮，填写项目名称和描述
2. 上传文件：在项目详情页点击"上传文件"，选择Java源码文件或包含完整项目的ZIP文件
3. 查看分析：系统会自动分析测试质量，并生成综合评分和详细报告
4. 查看细节：点击具体测试文件可查看详细分析和改进建议

## 系统架构

### 目录结构
```
java_test_quality_analyzer/
├── app.py                  # 主应用入口
├── config/                 # 配置文件
│   ├── __init__.py
│   └── config.py
├── models/                 # 数据模型
│   ├── __init__.py
│   └── database.py
├── services/               # 服务层
│   ├── __init__.py
│   └── openrouter_service.py  # OpenRouter API服务
├── static/                 # 静态资源
│   ├── css/
│   ├── js/
│   └── img/
├── templates/              # 前端模板
│   ├── layout/
│   ├── dashboard/
│   └── reports/
├── uploads/                # 上传文件存储目录
├── utils/                  # 工具函数
│   ├── __init__.py
│   ├── java_parser.py      # Java代码解析
│   └── test_analyzer.py    # 测试分析逻辑
├── requirements.txt        # 依赖包
├── .env                    # 环境变量（不纳入版本控制）
└── .env.example            # 环境变量示例
```

### 工作流程

1. **文件上传**：用户上传Java源代码和测试文件
2. **代码解析**：通过Tree-sitter和Pygments对Java代码进行语法解析
3. **测试识别**：识别测试类和测试方法
4. **规则评估**：根据10条原则对测试进行评估
5. **AI分析**：使用大语言模型进行深度语义分析和建议生成
6. **结果汇总**：汇总各项分析结果，生成综合评分
7. **报告生成**：生成可视化分析报告和改进建议

## 注意事项

- 需要有效的OpenRouter API密钥
- 建议上传完整项目结构以获得更准确的分析
- 对于大型项目，分析过程可能需要较长时间
- 可能需要针对API的请求限制进行适当调整

## 未来计划

- 支持更多编程语言的测试分析
- 添加团队协作功能
- 增加代码覆盖率分析
- 支持与CI/CD流程集成
- 优化AI分析精度和性能

## 致谢

- OpenRouter和通义千问提供的大语言模型支持
- Flask和SQLAlchemy等开源框架
- Bootstrap和Chart.js等前端库

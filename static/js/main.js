/**
 * Java Test Quality Analyzer
 * 主JavaScript文件 - 现代化交互体验
 */

// 页面性能优化设置
const appPerformance = {
    animations: true,
    reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
};

// 在文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化现代化功能
    initializeModernFeatures();
    
    // 初始化提示工具
    initializeTooltips();
    
    // 初始化文件上传增强
    enhanceFileUploads();
    
    // 添加代码高亮
    highlightCode();
    
    // 添加图表交互
    enhanceCharts();
    
    // 添加页面动画
    initializePageAnimations();
    
    // 初始化交互效果
    initializeInteractiveEffects();

    document.querySelectorAll('.show-code-snippet').forEach(function(link) {
        link.addEventListener('click', function(event) {
            event.preventDefault();
            const fileId = this.dataset.fileId;
            const lineNumber = this.dataset.line;
            const codeSnippetContainer = document.getElementById(`code-snippet-${lineNumber}`);

            if (codeSnippetContainer.style.display === 'block') {
                codeSnippetContainer.style.display = 'none';
                this.innerHTML = '<i class="fas fa-code"></i> 查看代码';
                return;
            }

            // 显示加载指示器
            codeSnippetContainer.innerHTML = '加载中...';
            codeSnippetContainer.style.display = 'block';
            this.innerHTML = '<i class="fas fa-code"></i> 隐藏代码';

            fetch(`/api/files/${fileId}/code_snippet?line=${lineNumber}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code) {
                        codeSnippetContainer.innerHTML = `<pre><code>${data.code}</code></pre>`;
                    } else if (data.error) {
                        codeSnippetContainer.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error fetching code snippet:', error);
                    codeSnippetContainer.innerHTML = '<div class="alert alert-danger">加载代码片段失败。</div>';
                });
        });
    });
});

/**
 * 初始化现代化功能
 */
function initializeModernFeatures() {
    // 添加导航栏滚动效果
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.15)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)';
            }
            
            lastScrollTop = scrollTop;
        });
    }
    
    // 添加页面加载进度条
    showLoadingProgress();
}

/**
 * 初始化页面动画
 */
function initializePageAnimations() {
    if (!appPerformance.animations || appPerformance.reducedMotion) return;
    
    // 为卡片添加入场动画
    const cards = document.querySelectorAll('.card');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    cards.forEach(card => {
        observer.observe(card);
    });
}

/**
 * 初始化交互效果
 */
function initializeInteractiveEffects() {
    // 按钮点击波纹效果
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            if (!appPerformance.animations) return;
            
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // 添加表格行悬停效果
    document.querySelectorAll('.table-hover tbody tr').forEach(row => {
        row.addEventListener('mouseenter', function() {
            if (!appPerformance.animations) return;
            this.style.transform = 'scale(1.01)';
        });
        
        row.addEventListener('mouseleave', function() {
            if (!appPerformance.animations) return;
            this.style.transform = 'scale(1)';
        });
    });
}

/**
 * 显示加载进度
 */
function showLoadingProgress() {
    const progressBar = document.createElement('div');
    progressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        z-index: 9999;
        transition: width 0.3s ease;
    `;
    
    document.body.appendChild(progressBar);
    
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 100);
    
    window.addEventListener('load', () => {
        clearInterval(interval);
        progressBar.style.width = '100%';
        setTimeout(() => {
            progressBar.style.opacity = '0';
            // 确保在透明度动画结束后移除元素
            setTimeout(() => {
                progressBar.remove();
            }, 300); 
        }, 500);
    });

    // Fallback in case 'load' event doesn't fire as expected or for faster removal
    // This will ensure the progress bar is removed/hidden even if load event is delayed
    document.addEventListener('readystatechange', () => {
        if (document.readyState === 'complete') {
            clearInterval(interval);
            progressBar.style.width = '100%';
            setTimeout(() => {
                progressBar.style.opacity = '0';
                setTimeout(() => {
                    progressBar.remove();
                }, 300); 
            }, 500);
        }
    });
}

/**
 * 初始化Bootstrap提示工具
 */
function initializeTooltips() {
    // 找到所有带有data-bs-toggle="tooltip"属性的元素
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 增强文件上传体验
 */
function enhanceFileUploads() {
    // 获取上传区域元素
    const uploadAreas = document.querySelectorAll('.upload-area');
    
    uploadAreas.forEach(area => {
        // 找到上传区域内的文件输入元素
        const fileInput = area.querySelector('input[type="file"]');
        
        if (fileInput) {
            // 添加拖拽支持
            area.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            area.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            area.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    updateFileDisplay(area, files[0]);
                }
            });
            
            // 当点击上传区域时，触发文件输入的点击事件
            area.addEventListener('click', function(e) {
                if (e.target !== fileInput) {
                    e.preventDefault();
                    fileInput.click();
                }
            });
            
            // 当文件选择改变时，显示文件名
            fileInput.addEventListener('change', function() {
                if (this.files && this.files.length > 0) {
                    updateFileDisplay(area, this.files[0]);
                }
            });
        }
    });
}

/**
 * 更新文件显示
 */
function updateFileDisplay(area, file) {
    // 移除之前的文件显示
    const existingDisplay = area.querySelector('.file-display');
    if (existingDisplay) {
        existingDisplay.remove();
    }
    
    // 创建新的文件显示
    const fileDisplay = document.createElement('div');
    fileDisplay.classList.add('file-display', 'mt-3', 'p-3', 'rounded');
    fileDisplay.style.background = 'rgba(102, 126, 234, 0.1)';
    fileDisplay.style.border = '1px solid rgba(102, 126, 234, 0.2)';
    
    fileDisplay.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-file-code fa-2x me-3" style="color: #667eea;"></i>
            <div>
                <strong class="text-primary">${file.name}</strong>
                <div class="small text-muted">${formatFileSize(file.size)}</div>
            </div>
        </div>
    `;
    
    area.appendChild(fileDisplay);
    
    // 添加动画效果
    fileDisplay.style.opacity = '0';
    fileDisplay.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        fileDisplay.style.transition = 'all 0.3s ease';
        fileDisplay.style.opacity = '1';
        fileDisplay.style.transform = 'translateY(0)';
    }, 10);
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 为代码块添加高亮效果
 */
function highlightCode() {
    // 如果页面中存在code元素并且hljs可用，应用代码高亮
    if (document.querySelectorAll('pre code').length > 0 && typeof hljs !== 'undefined') {
        hljs.highlightAll();
    }
}

/**
 * 增强图表的交互性
 */
function enhanceCharts() {
    // 如果页面中有Chart.js图表，添加交互
    document.querySelectorAll('canvas[id$="Chart"]').forEach(canvas => {
        // 通过ID获取图表实例（如果已存在）
        const chartInstance = Chart.getChart(canvas.id);
        
        if (chartInstance) {
            // 为图表添加点击事件
            canvas.onclick = function(evt) {
                const points = chartInstance.getElementsAtEventForMode(evt, 'nearest', { intersect: true }, false);
                
                if (points.length) {
                    const firstPoint = points[0];
                    const label = chartInstance.data.labels[firstPoint.index];
                    const value = chartInstance.data.datasets[firstPoint.datasetIndex].data[firstPoint.index];
                    
                    console.log(`点击了 ${label}，值为 ${value}`);
                    
                    // 这里可以添加更多的交互逻辑，例如过滤相关数据或导航到详情页面
                }
            };
        }
    });
}

/**
 * 用于定时更新的计时器
 * @param {Function} updateFn 更新函数
 * @param {number} interval 间隔时间（毫秒）
 */
function startUpdateTimer(updateFn, interval = 5000) {
    setInterval(updateFn, interval);
}

/**
 * 显示分析进度弹窗
 */
function showAnalysisProgress() {
    // 移除已存在的进度弹窗
    const existingModal = document.getElementById('analysisProgressModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // 创建进度弹窗
    const modal = document.createElement('div');
    modal.id = 'analysisProgressModal';
    modal.className = 'modal fade show';
    modal.style.cssText = `
        display: block;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(5px);
        z-index: 2000;
    `;
    
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border: none; border-radius: 16px; overflow: hidden;">
                <div class="modal-header" style="background: var(--primary); color: white; border: none;">
                    <h5 class="modal-title">
                        <i class="fas fa-cogs me-2 fa-spin"></i>智能分析进行中
                    </h5>
                </div>
                <div class="modal-body p-4">
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-brain fa-3x pulse" style="color: #667eea;"></i>
                        </div>
                        <h6 id="progressStatus">正在初始化分析...</h6>
                        <p class="text-muted" id="progressDetail">大语言模型正在深度理解您的代码</p>
                    </div>
                    
                    <div class="progress mb-3" style="height: 8px; border-radius: 10px;">
                        <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                             style="width: 0%; background: var(--primary); border-radius: 10px;"></div>
                    </div>
                    
                    <div class="d-flex justify-content-between small text-muted">
                        <span id="progressPercent">0%</span>
                        <span id="progressTime">预计剩余: --</span>
                    </div>
                    
                    <div class="mt-4">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="p-2 rounded" style="background: rgba(102, 126, 234, 0.1);">
                                    <i class="fas fa-file-code fa-lg mb-1" style="color: #667eea;"></i>
                                    <div class="small">代码解析</div>
                                    <div class="tiny text-success" id="step1">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="p-2 rounded" style="background: rgba(79, 172, 254, 0.1);">
                                    <i class="fas fa-brain fa-lg mb-1" style="color: #4facfe;"></i>
                                    <div class="small">AI分析</div>
                                    <div class="tiny text-muted" id="step2">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="p-2 rounded" style="background: rgba(34, 193, 195, 0.1);">
                                    <i class="fas fa-chart-line fa-lg mb-1" style="color: #22c1c3;"></i>
                                    <div class="small">生成报告</div>
                                    <div class="tiny text-muted" id="step3">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4 mb-0" style="border: none; background: rgba(79, 172, 254, 0.1);">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>分析提示：</strong>我们正在使用先进的大语言模型对您的代码进行深度分析，请稍等片刻...
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 返回更新进度的函数
    return {
        updateProgress: function(message, detail, percent) {
            const statusEl = document.getElementById('progressStatus');
            const detailEl = document.getElementById('progressDetail');
            const progressBar = document.getElementById('progressBar');
            const progressPercent = document.getElementById('progressPercent');
            const progressTime = document.getElementById('progressTime');
            
            if (statusEl) statusEl.textContent = message;
            if (detailEl) detailEl.textContent = detail;
            if (progressBar) progressBar.style.width = percent + '%';
            if (progressPercent) progressPercent.textContent = percent + '%';
            
            // 更新步骤状态
            if (percent >= 20) {
                document.getElementById('step1').innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            }
            if (percent >= 50) {
                document.getElementById('step2').innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            }
            if (percent >= 90) {
                document.getElementById('step3').innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            }
            
            // 估算剩余时间
            if (percent > 0 && percent < 100) {
                const estimatedRemaining = Math.max(0, Math.ceil((100 - percent) * 2 / 100)); // 简单估算
                if (progressTime) {
                    progressTime.textContent = `预计剩余: ${estimatedRemaining} 分钟`;
                }
            }
        },
        
        close: function() {
            modal.style.animation = 'fadeOut 0.3s ease';
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.remove();
                }
            }, 300);
        }
    };
}

/**
 * 分析完成后的成功提示
 */
function showAnalysisComplete(result) {
    const modal = document.createElement('div');
    modal.className = 'modal fade show';
    modal.style.cssText = `
        display: block;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(5px);
        z-index: 2000;
    `;
    
    const grade = result.grade || {level: 'B', description: '良好'};
    const gradeColor = grade.level === 'A+' || grade.level === 'A' ? 'success' : 
                      grade.level === 'B' ? 'info' : 
                      grade.level === 'C' ? 'warning' : 'danger';
    
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border: none; border-radius: 16px; overflow: hidden;">
                <div class="modal-header bg-success text-white" style="border: none;">
                    <h5 class="modal-title">
                        <i class="fas fa-check-circle me-2"></i>分析完成
                    </h5>
                    <button type="button" class="btn-close btn-close-white" onclick="this.closest('.modal').remove()"></button>
                </div>
                <div class="modal-body p-4 text-center">
                    <div class="mb-4">
                        <div class="display-4 fw-bold text-${gradeColor} mb-2">${result.score || 0}</div>
                        <div class="badge bg-${gradeColor} fs-6 px-3 py-2">
                            ${grade.level} - ${grade.description}
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-4">
                            <div class="p-3 rounded" style="background: rgba(40, 167, 69, 0.1);">
                                <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                                <div class="fw-bold">${result.method_count || 0}</div>
                                <div class="small text-muted">测试方法</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-3 rounded" style="background: rgba(220, 53, 69, 0.1);">
                                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                <div class="fw-bold">${Object.keys(result.violations || {}).length}</div>
                                <div class="small text-muted">问题规则</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-3 rounded" style="background: rgba(13, 110, 253, 0.1);">
                                <i class="fas fa-lightbulb fa-2x text-primary mb-2"></i>
                                <div class="fw-bold">${(result.suggestions?.quick_fixes || []).length}</div>
                                <div class="small text-muted">改进建议</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mb-4" style="border: none; background: rgba(13, 110, 253, 0.1);">
                        <i class="fas fa-info-circle me-2"></i>
                        ${result.suggestions?.overall_advice || '分析完成，请查看详细报告'}
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-lg" onclick="window.location.reload()">
                            <i class="fas fa-chart-line me-2"></i>查看详细报告
                        </button>
                        <button class="btn btn-outline-secondary" onclick="this.closest('.modal').remove()">
                            稍后查看
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 自动关闭
    setTimeout(() => {
        if (modal.parentNode) {
            modal.style.animation = 'fadeOut 0.3s ease';
            setTimeout(() => modal.remove(), 300);
        }
    }, 10000);
}

/**
 * 创建现代化通知
 * @param {string} title 通知标题
 * @param {string} message 通知内容
 * @param {string} type 通知类型 (success, warning, error, info)
 */
function createNotification(title, message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 1050;
        max-width: 400px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        border: none;
        animation: slideInRight 0.3s ease;
    `;
    
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${getIconForType(type)} me-2"></i>
            <div>
                <strong>${title}</strong>
                <div class="small">${message}</div>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // 自动消失
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

/**
 * 获取通知类型对应的图标
 */
function getIconForType(type) {
    const icons = {
        success: 'check-circle',
        warning: 'exclamation-triangle',
        error: 'times-circle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * 数字动画计数器
 */
function animateCounter(element, target, duration = 2000) {
    if (!appPerformance.animations) {
        element.textContent = target;
        return;
    }
    
    const start = 0;
    const startTime = window.performance.now();
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (target - start) * easeOutCubic(progress));
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

/**
 * 缓动函数
 */
function easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
}

// 添加CSS动画样式
const animationStyles = document.createElement('style');
animationStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        pointer-events: none;
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .upload-area.dragover {
        border-color: rgba(102, 126, 234, 0.8) !important;
        background: rgba(102, 126, 234, 0.15) !important;
        transform: scale(1.02);
    }
`;
document.head.appendChild(animationStyles);
/* 本地字体已在base.html中引用，无需外部导入 */

/* CSS变量定义 */
:root {
    --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --warning: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
    --info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --light: #f8fafc;
    --dark: #1e293b;
    --surface: rgba(255, 255, 255, 0.95);
    --border-radius: 16px;
    --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主要样式 */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: var(--dark);
    overflow-x: hidden;
}

/* 背景装饰 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
}

/* 页头样式 */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    background: var(--primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none !important;
}

.navbar-nav .nav-link {
    color: var(--dark) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 8px;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: var(--primary);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

/* 卡片样式 */
.card {
    margin-bottom: 2rem;
    border: none;
    border-radius: var(--border-radius);
    background: var(--surface);
    backdrop-filter: blur(20px);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary);
    transform: scaleX(0);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.card:hover::before {
    transform: scaleX(1);
}

.card-header {
    font-weight: 600;
    background: transparent !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

.card-header.bg-primary {
    background: var(--primary) !important;
    color: white !important;
    border: none;
}

.card-header.bg-info {
    background: var(--info) !important;
    color: var(--dark) !important;
}

.card-header.bg-success {
    background: var(--success) !important;
    color: white !important;
}

.card-body {
    padding: 2rem;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

/* 进度条样式 */
.progress {
    height: 10px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

/* 表格样式 */
.table {
    border-collapse: separate;
    border-spacing: 0;
    background: transparent;
}

.table th {
    font-weight: 600;
    background: rgba(102, 126, 234, 0.1);
    color: var(--dark);
    border: none;
    padding: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.table td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.table-hover tbody tr {
    transition: var(--transition);
}

.table-hover tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.table-responsive {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

/* 列表组样式 */
.list-group-item {
    border-left: none;
    border-right: none;
    padding: 12px 15px;
}

.list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}

.list-group-item:last-child {
    border-bottom-left-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

/* 徽章样式 */
.badge {
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    overflow: hidden;
}

.badge.bg-success {
    background: var(--success) !important;
    color: white;
}

.badge.bg-warning {
    background: var(--warning) !important;
    color: white;
}

.badge.bg-danger {
    background: var(--danger) !important;
    color: white;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    color: white;
}

/* 按钮样式 */
.btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border: none;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-success {
    background: var(--success);
    color: white;
}

.btn-info {
    background: var(--info);
    color: var(--dark);
}

.btn-warning {
    background: var(--warning);
    color: white;
}

.btn-danger {
    background: var(--danger);
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    border-radius: 8px;
}

/* 表单样式 */
.form-control {
    padding: 1rem 1.25rem;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
    font-weight: 500;
}

.form-control:focus {
    border-color: transparent;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    background: white;
    transform: translateY(-2px);
}

.form-label {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

/* 上传区域样式 */
.upload-area {
    cursor: pointer;
    transition: var(--transition);
    border: 2px dashed rgba(102, 126, 234, 0.3);
    border-radius: var(--border-radius);
    background: rgba(102, 126, 234, 0.05);
    padding: 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary);
    opacity: 0;
    transition: var(--transition);
}

.upload-area:hover {
    border-color: rgba(102, 126, 234, 0.6);
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-4px);
    box-shadow: var(--box-shadow);
}

.upload-area:hover::before {
    opacity: 0.05;
}

/* 页脚样式 */
.footer {
    margin-top: 4rem;
    padding: 2rem 0;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.footer .text-muted {
    color: var(--dark) !important;
    font-weight: 500;
}

.footer a {
    color: var(--dark);
    text-decoration: none;
    font-weight: 600;
    background: var(--primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--transition);
}

.footer a:hover {
    transform: translateY(-1px);
}

/* 面包屑导航样式 */
.breadcrumb {
    background-color: transparent;
    padding: 0.75rem 0;
}

/* 代码块样式 */
pre {
    background: rgba(30, 41, 59, 0.95);
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    overflow-x: auto;
    margin: 1.5rem 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--box-shadow);
    backdrop-filter: blur(10px);
}

code {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 500;
}

/* 警告框样式 */
.alert {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background: rgba(79, 172, 254, 0.1);
    color: #0891b2;
    border-left: 4px solid #0891b2;
}

.alert-warning {
    background: rgba(253, 187, 45, 0.1);
    color: #d97706;
    border-left: 4px solid #d97706;
}

.alert-danger {
    background: rgba(247, 112, 154, 0.1);
    color: #dc2626;
    border-left: 4px solid #dc2626;
}

/* 导航标签样式 */
.nav-tabs {
    border-bottom: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding: 0.5rem;
}

.nav-tabs .nav-link {
    color: var(--dark);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    background: transparent;
}

.nav-tabs .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    background: var(--primary);
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    border-top-color: #667eea;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 容器样式 */
.container {
    position: relative;
    z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* Set z-index for modal and backdrop to ensure correct layering */
.modal {
    z-index: 1050;
}

.modal-backdrop {
    z-index: 1040;
    pointer-events: none; /* Ensure backdrop does not block clicks */
    background-color: rgba(0, 0, 0, 0.3); /* Adjust for desired darkness */
}

/* Ensure all elements inside the modal are interactive */
.modal * {
    pointer-events: auto;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local('Inter Light'), local('Inter-Light'),
       local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont'),
       local('Segoe UI'), local('Roboto'), local('Oxygen'),
       local('Ubuntu'), local('Cantarell'), local('Fira Sans'),
       local('Droid Sans'), local('Helvetica Neue'), local('sans-serif');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Inter Regular'), local('Inter-Regular'),
       local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont'),
       local('Segoe UI'), local('Roboto'), local('Oxygen'),
       local('Ubuntu'), local('Cantarell'), local('Fira Sans'),
       local('Droid Sans'), local('Helvetica Neue'), local('sans-serif');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local('Inter Medium'), local('Inter-Medium'),
       local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont'),
       local('Segoe UI'), local('Roboto'), local('Oxygen'),
       local('Ubuntu'), local('Cantarell'), local('Fira Sans'),
       local('Droid Sans'), local('Helvetica Neue'), local('sans-serif');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: local('Inter SemiBold'), local('Inter-SemiBold'),
       local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont'),
       local('Segoe UI'), local('Roboto'), local('Oxygen'),
       local('Ubuntu'), local('Cantarell'), local('Fira Sans'),
       local('Droid Sans'), local('Helvetica Neue'), local('sans-serif');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local('Inter Bold'), local('Inter-Bold'),
       local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont'),
       local('Segoe UI'), local('Roboto'), local('Oxygen'),
       local('Ubuntu'), local('Cantarell'), local('Fira Sans'),
       local('Droid Sans'), local('Helvetica Neue'), local('sans-serif');
}

/* Fallback body font stack */
body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}
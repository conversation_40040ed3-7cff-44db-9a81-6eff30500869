import requests
import hashlib
import json

url = 'http://inmmcuat.xiaopuuat.com:13600/reflect/mmc/llm/app/chat/completion'

data = {
	"query“: query, 
	”history“:[], 
	"stream”: True,
	"chat_type":"sync",
	app_id:"app_20250709104215697_908735",
	username:"uatgw06541"
}

api_secret = 'edf35acd3d9c4f66b82a1398e3ec769e'

def md5_encrypt(text):
	md5_obj = hashlib.md5(text.encode('utf8'))
	return md5_obj.hexdigest()

signature = md5_encrypt（data["username"] + data["query"] + api_secret）

headers = {
	"Content-Type":"application/json; charset=UTF-8",
	"x-api-key": 'UTMP',
	"x-signature":signature
}

res = requests.post(url,headers=headers,json=data)
print(res.text)

chat_type:为同步调用（sync）时，接口返回结果如下：

SSE 流式响应接口返回:	I	
event: add
data: {"headers":{},"body":{"code":200,"data":{"session id":"chat_20240415171021902_508513","answer_id":"answer20240415171021957_768291","content":"我","reference_size":0,"sn":1},"message":"success"}} event: add
data: {"headers":{},"body" :{"code":200,"data":{"session id":"chat_20240415171021902_508513","answer_id":"answer20240415171021957_768291","content":"是一个","reference_s ize":0,"sn":1},"message":"success"}} 
event: finish
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class ModelConfig(db.Model):
    """模型配置表"""
    __tablename__ = 'model_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    model_type = db.Column(db.String(50), nullable=False, unique=True)  # 模型类型 (zhipu, enterprise)
    model_name = db.Column(db.String(100), nullable=False)  # 模型显示名称
    is_enabled = db.Column(db.<PERSON>, default=True)  # 是否启用
    is_default = db.Column(db.<PERSON>, default=False)  # 是否为默认模型
    config_data = db.Column(db.Text, nullable=True)  # JSON格式的配置数据
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<ModelConfig {self.model_type}>'
    
    def get_config_data(self):
        """获取配置数据"""
        if self.config_data:
            return json.loads(self.config_data)
        return {}
    
    def set_config_data(self, config_dict):
        """设置配置数据"""
        self.config_data = json.dumps(config_dict)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'model_type': self.model_type,
            'model_name': self.model_name,
            'is_enabled': self.is_enabled,
            'is_default': self.is_default,
            'config_data': self.get_config_data(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Project(db.Model):
    """项目模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    analyses = db.relationship('Analysis', backref='project', lazy=True, cascade='all, delete-orphan', order_by='desc(Analysis.created_at)')
    
    def __repr__(self):
        return f'<Project {self.name}>'


class Analysis(db.Model):
    """分析记录模型"""
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    score = db.Column(db.Float, nullable=False)  # 总评分
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 规则评分和违反详情（JSON格式存储）
    rule_scores = db.Column(db.Text, nullable=False)  # JSON格式存储各规则得分
    rule_violations = db.Column(db.Text, nullable=False)  # JSON格式存储按原则统计的违规数量及简要信息
    
    # 关联关系
    test_files = db.relationship('TestFile', backref='analysis', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Analysis {self.id} for Project {self.project_id}>'
    
    def get_rule_scores(self):
        """获取规则评分"""
        return json.loads(self.rule_scores)
    
    def set_rule_scores(self, scores_dict):
        """设置规则评分"""
        self.rule_scores = json.dumps(scores_dict)
    
    def get_rule_violations(self):
        """获取规则违反详情"""
        return json.loads(self.rule_violations)
    
    def set_rule_violations(self, violations_dict):
        """设置规则违反详情"""
        self.rule_violations = json.dumps(violations_dict)


class TestFile(db.Model):
    """测试文件模型"""
    id = db.Column(db.Integer, primary_key=True)
    analysis_id = db.Column(db.Integer, db.ForeignKey('analysis.id'), nullable=False)
    file_name = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)  # 相对路径
    is_test_file = db.Column(db.Boolean, default=True)  # 是否为测试文件，否则为源代码文件
    score = db.Column(db.Float, nullable=True)  # 该文件的评分
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 文件分析结果（JSON格式）
    analysis_result = db.Column(db.Text, nullable=True)  # JSON格式存储详细的行级分析结果和违规信息
    
    def __repr__(self):
        return f'<TestFile {self.file_name}>'
    
    def get_analysis_result(self):
        """获取分析结果"""
        if self.analysis_result:
            return json.loads(self.analysis_result)
        return {}
    
    def set_analysis_result(self, result_dict):
        """设置分析结果"""
        self.analysis_result = json.dumps(result_dict)

# 智谱AI API配置
# 请在智谱AI开放平台 (https://open.bigmodel.cn/) 获取API密钥
# 新版API密钥格式应以"zhipu-"开头，例如: zhipu-1234567890abcdef
ZHIPUAI_API_KEY=zhipu-your-api-key-here

# Flask配置
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=change_this_to_a_random_string

# 数据库配置
DATABASE_URI=sqlite:///test_quality.db

# 存储配置
UPLOAD_FOLDER=uploads
ALLOWED_EXTENSIONS=java,txt,md

# 说明：
# 1. 复制此文件为 .env 并填入实际的API密钥
# 2. 如果没有设置ZHIPUAI_API_KEY，系统将使用基于规则的分析
# 3. 设置了有效的API密钥后，系统将使用智谱AI GLM-4进行代码分析

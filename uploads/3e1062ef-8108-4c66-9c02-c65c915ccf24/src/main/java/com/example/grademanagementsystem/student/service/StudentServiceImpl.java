package com.example.grademanagementsystem.student.service;

import com.example.grademanagementsystem.student.model.Student;
import com.example.grademanagementsystem.student.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

@Service
public class StudentServiceImpl implements StudentService {

    // Using a simple in-memory map as a placeholder for a database
    private final Map<String, Student> studentBook = new ConcurrentHashMap<>();

    @Override
    public Student addStudent(Student student) {
        // In a real application, this would interact with a database via StudentRepository
        studentBook.put(student.getStudentId(), student);
        return student;
    }

    @Override
    public Optional<Student> getStudentById(String studentId) {
        return Optional.ofNullable(studentBook.get(studentId));
    }

    @Override
    public List<Student> getAllStudents() {
        return new ArrayList<>(studentBook.values());
    }

    @Override
    public Student updateStudent(String studentId, Student studentDetails) {
        if (studentBook.containsKey(studentId)) {
            studentBook.put(studentId, studentDetails);
            return studentDetails;
        }
        return null; // Or throw an exception if student not found
    }

    @Override
    public void deleteStudent(String studentId) {
        studentBook.remove(studentId);
    }
} 
package com.example.grademanagementsystem.student.repository;

import com.example.grademanagementsystem.student.model.Student;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StudentRepository {
    Student save(Student student);
    Optional<Student> findByStudentId(String studentId);
    List<Student> findAll();
    void deleteByStudentId(String studentId);
} 
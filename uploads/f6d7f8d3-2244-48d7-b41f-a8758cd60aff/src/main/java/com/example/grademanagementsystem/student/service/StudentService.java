package com.example.grademanagementsystem.student.service;

import com.example.grademanagementsystem.student.model.Student;

import java.util.List;
import java.util.Optional;

public interface StudentService {
    Student addStudent(Student student);
    Optional<Student> getStudentById(String studentId);
    List<Student> getAllStudents();
    Student updateStudent(String studentId, Student studentDetails);
    void deleteStudent(String studentId);
} 
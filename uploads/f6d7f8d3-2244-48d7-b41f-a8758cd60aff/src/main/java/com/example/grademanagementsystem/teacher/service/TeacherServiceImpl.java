package com.example.grademanagementsystem.teacher.service;

import com.example.grademanagementsystem.teacher.model.Teacher;
import com.example.grademanagementsystem.teacher.repository.TeacherRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

@Service
public class TeacherServiceImpl implements TeacherService {

    // Using a simple in-memory map as a placeholder for a database
    private final Map<String, Teacher> teacherBook = new ConcurrentHashMap<>();

    @Override
    public Teacher addTeacher(Teacher teacher) {
        // In a real application, this would interact with a database via TeacherRepository
        teacherBook.put(teacher.getTeacherId(), teacher);
        return teacher;
    }

    @Override
    public Optional<Teacher> getTeacherById(String teacherId) {
        return Optional.ofNullable(teacherBook.get(teacherId));
    }

    @Override
    public List<Teacher> getAllTeachers() {
        return new ArrayList<>(teacherBook.values());
    }

    @Override
    public Teacher updateTeacher(String teacherId, Teacher teacherDetails) {
        if (teacherBook.containsKey(teacherId)) {
            teacherBook.put(teacherId, teacherDetails);
            return teacherDetails;
        }
        return null; // Or throw an exception if teacher not found
    }

    @Override
    public void deleteTeacher(String teacherId) {
        teacherBook.remove(teacherId);
    }
} 
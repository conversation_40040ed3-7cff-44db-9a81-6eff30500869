package com.example.grademanagementsystem.teacher.service;

import com.example.grademanagementsystem.teacher.model.Teacher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class TeacherServiceImplTest {

    @InjectMocks
    private TeacherServiceImpl teacherService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Resetting the in-memory data for each test
        teacherService = new TeacherServiceImpl();
    }

    @Test
    void addTeacher_shouldAddNewTeacher() {
        Teacher teacher = new Teacher("T001", "Mr. <PERSON>", "Math");
        Teacher addedTeacher = teacherService.addTeacher(teacher);
        assertNotNull(addedTeacher, "Added teacher should not be null");
        assertEquals("T001", addedTeacher.getTeacherId(), "Teacher ID should match");
        assertEquals("Mr. <PERSON>", addedTeacher.getName(), "Teacher name should match");
        assertEquals("Math", addedTeacher.getSubject(), "Teacher subject should match");

        Optional<Teacher> retrievedTeacher = teacherService.getTeacherById("T001");
        assertTrue(retrievedTeacher.isPresent(), "Teacher should be retrievable after adding");
        assertEquals(teacher.getName(), retrievedTeacher.get().getName(), "Retrieved teacher name should match added teacher name");
    }

    @Test
    void getTeacherById_shouldReturnTeacher_whenExists() {
        teacherService.addTeacher(new Teacher("T002", "Ms. Jones", "English"));
        Optional<Teacher> foundTeacher = teacherService.getTeacherById("T002");
        assertTrue(foundTeacher.isPresent(), "Teacher should be found");
        assertEquals("Ms. Jones", foundTeacher.get().getName(), "Name should match");
    }

    @Test
    void getTeacherById_shouldReturnEmpty_whenNotExists() {
        Optional<Teacher> foundTeacher = teacherService.getTeacherById("T999");
        assertFalse(foundTeacher.isPresent(), "Teacher should not be found");
    }

    @Test
    void getAllTeachers_shouldReturnAllAddedTeachers() {
        teacherService.addTeacher(new Teacher("T003", "Mr. Brown", "Chinese"));
        teacherService.addTeacher(new Teacher("T004", "Dr. White", "Science"));
        List<Teacher> allTeachers = teacherService.getAllTeachers();
        assertEquals(2, allTeachers.size(), "Should return two teachers");
    }

    @Test
    void updateTeacher_shouldModifyExistingTeacher() {
        teacherService.addTeacher(new Teacher("T005", "Mrs. Green", "History"));
        Teacher updatedDetails = new Teacher("T005", "Mrs. Green-Lee", "Geography");
        Teacher result = teacherService.updateTeacher("T005", updatedDetails);
        assertNotNull(result, "Result of update should not be null");
        assertEquals("Mrs. Green-Lee", result.getName(), "Name should be updated");
        assertEquals("Geography", result.getSubject(), "Subject should be updated");

        Optional<Teacher> retrievedTeacher = teacherService.getTeacherById("T005");
        assertTrue(retrievedTeacher.isPresent(), "Updated teacher should be retrievable");
        assertEquals("Mrs. Green-Lee", retrievedTeacher.get().getName(), "Retrieved teacher name should reflect update");
    }

    @Test
    void updateTeacher_shouldReturnNull_whenTeacherNotExists() {
        Teacher updatedDetails = new Teacher("T999", "NonExistent", "Art");
        Teacher result = teacherService.updateTeacher("T999", updatedDetails);
        assertNull(result, "Result should be null if teacher does not exist");
    }

    @Test
    void deleteTeacher_shouldRemoveTeacher() {
        teacherService.addTeacher(new Teacher("T006", "Mr. Black", "PE"));
        teacherService.deleteTeacher("T006");
        Optional<Teacher> foundTeacher = teacherService.getTeacherById("T006");
        assertFalse(foundTeacher.isPresent(), "Teacher should be deleted");
    }

    @Test
    void addTeacher_shouldAddTeacher_missingAssertion() {
        Teacher teacher = new Teacher("T007", "Miss Daisy", "Music");
        teacherService.addTeacher(teacher);
        // This test intentionally lacks a direct assertion to demonstrate a missing assertion.
        // A proper test would assert that the teacher was successfully added and can be retrieved.
    }

    @Test
    void updateTeacher_checkReturnObject_ineffectiveAssertion() {
        Teacher initialTeacher = new Teacher("T008", "Mr. Cloud", "IT");
        teacherService.addTeacher(initialTeacher);

        Teacher updatedDetails = new Teacher("T008", "Mr. Cloud P.", "IT Security");
        Teacher result = teacherService.updateTeacher("T008", updatedDetails);

        // This assertion checks if the returned object is not null, but doesn't verify its content.
        // It is an ineffective assertion if the goal is to confirm the specific updated values.
        assertNotNull(result, "Result of update should not be null");
    }
} 
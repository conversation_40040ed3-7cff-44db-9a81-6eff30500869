package com.example.grademanagementsystem.grade.service;

import com.example.grademanagementsystem.grade.model.Grade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class GradeServiceImplTest {

    @InjectMocks
    private GradeServiceImpl gradeService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Resetting the in-memory data for each test
        gradeService = new GradeServiceImpl();
    }

    @Test
    void addGrade_shouldAddNewGrade() {
        Grade grade = new Grade("S001", "Math", 90, 85, 92);
        Grade addedGrade = gradeService.addGrade(grade);
        assertNotNull(addedGrade, "Added grade should not be null");
        assertEquals("S001", addedGrade.getStudentId(), "Student ID should match");
        assertEquals("Math", addedGrade.getCourse(), "Course should match");
        assertEquals(90, addedGrade.getChineseScore(), "Chinese score should match");
        assertEquals(85, addedGrade.getMathScore(), "Math score should match");
        assertEquals(92, addedGrade.getEnglishScore(), "English score should match");

        Optional<Grade> retrievedGrade = gradeService.getGrade("S001", "Math");
        assertTrue(retrievedGrade.isPresent(), "Grade should be retrievable after adding");
        assertEquals(grade.getChineseScore(), retrievedGrade.get().getChineseScore(), "Retrieved grade chinese score should match added grade chinese score");
        assertEquals(grade.getMathScore(), retrievedGrade.get().getMathScore(), "Retrieved grade math score should match added grade math score");
        assertEquals(grade.getEnglishScore(), retrievedGrade.get().getEnglishScore(), "Retrieved grade english score should match added grade english score");
    }

    @Test
    void getGrade_shouldReturnGrade_whenExists() {
        gradeService.addGrade(new Grade("S002", "Science", 75, 80, 85));
        Optional<Grade> foundGrade = gradeService.getGrade("S002", "Science");
        assertTrue(foundGrade.isPresent(), "Grade should be found");
        assertEquals(75, foundGrade.get().getChineseScore(), "Chinese score should match");
        assertEquals(80, foundGrade.get().getMathScore(), "Math score should match");
        assertEquals(85, foundGrade.get().getEnglishScore(), "English score should match");
    }

    @Test
    void getGrade_shouldReturnEmpty_whenNotExists() {
        Optional<Grade> foundGrade = gradeService.getGrade("S999", "NonExistentCourse");
        assertFalse(foundGrade.isPresent(), "Grade should not be found");
    }

    @Test
    void getAllGrades_shouldReturnAllAddedGrades() {
        gradeService.addGrade(new Grade("S003", "History", 70, 72, 74));
        gradeService.addGrade(new Grade("S004", "Art", 95, 90, 88));
        List<Grade> allGrades = gradeService.getAllGrades();
        assertEquals(2, allGrades.size(), "Should return two grades");
    }

    @Test
    void updateGrade_shouldModifyExistingGrade() {
        gradeService.addGrade(new Grade("S005", "English", 80, 82, 84));
        Grade updatedGrade = gradeService.updateGrade("S005", "English", 88, 90, 92);
        assertNotNull(updatedGrade, "Result of update should not be null");
        assertEquals(88, updatedGrade.getChineseScore(), "Chinese score should be updated");
        assertEquals(90, updatedGrade.getMathScore(), "Math score should be updated");
        assertEquals(92, updatedGrade.getEnglishScore(), "English score should be updated");

        Optional<Grade> retrievedGrade = gradeService.getGrade("S005", "English");
        assertTrue(retrievedGrade.isPresent(), "Updated grade should be retrievable");
        assertEquals(88, retrievedGrade.get().getChineseScore(), "Retrieved chinese score should reflect update");
        assertEquals(90, retrievedGrade.get().getMathScore(), "Retrieved math score should reflect update");
        assertEquals(92, retrievedGrade.get().getEnglishScore(), "Retrieved english score should reflect update");
    }

    @Test
    void updateGrade_shouldReturnNull_whenGradeNotExists() {
        Grade result = gradeService.updateGrade("S999", "NonExistentCourse", 100, 100, 100);
        assertNull(result, "Result should be null if grade does not exist");
    }

    @Test
    void deleteGrade_shouldRemoveGrade() {
        gradeService.addGrade(new Grade("S006", "PE", 75, 78, 80));
        gradeService.deleteGrade("S006", "PE");
        Optional<Grade> foundGrade = gradeService.getGrade("S006", "PE");
        assertFalse(foundGrade.isPresent(), "Grade should be deleted");
    }

    @Test
    void addGrade_shouldAddGrade_missingAssertion() {
        Grade grade = new Grade("S007", "Physics", 85, 88, 90);
        gradeService.addGrade(grade);
        // This test intentionally lacks a direct assertion to demonstrate a missing assertion.
        // A proper test would assert that the grade was successfully added and can be retrieved.
    }

    @Test
    void updateGrade_checkSideEffect_ineffectiveAssertion() {
        Grade initialGrade = new Grade("S008", "Art", 60, 65, 70);
        gradeService.addGrade(initialGrade);

        // Update only the Chinese score
        gradeService.updateGrade("S008", "Art", 90, 65, 70);

        // This assertion might be considered ineffective if the goal is to specifically verify
        // that only Chinese score was updated and others remained the same. While it passes,
        // it doesn't explicitly check the other scores which is a side effect.
        // A more robust test would assert values for math and english scores explicitly.
        assertTrue(gradeService.getGrade("S008", "Art").isPresent());
    }
}
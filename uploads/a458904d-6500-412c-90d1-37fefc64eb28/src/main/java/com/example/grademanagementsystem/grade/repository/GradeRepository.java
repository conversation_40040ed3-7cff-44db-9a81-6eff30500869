package com.example.grademanagementsystem.grade.repository;

import com.example.grademanagementsystem.grade.model.Grade;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface GradeRepository {
    Grade save(Grade grade);
    Optional<Grade> findByStudentIdAndCourse(String studentId, String course);
    List<Grade> findAll();
    void deleteByStudentIdAndCourse(String studentId, String course);
}
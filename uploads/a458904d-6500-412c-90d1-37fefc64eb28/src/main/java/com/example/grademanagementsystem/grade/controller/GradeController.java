package com.example.grademanagementsystem.grade.controller;

import com.example.grademanagementsystem.grade.model.Grade;
import com.example.grademanagementsystem.grade.service.GradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/grades")
public class GradeController {

    private final GradeService gradeService;

    @Autowired
    public GradeController(GradeService gradeService) {
        this.gradeService = gradeService;
    }

    @PostMapping
    public ResponseEntity<Grade> addGrade(@RequestBody Grade grade) {
        Grade newGrade = gradeService.addGrade(grade);
        return new ResponseEntity<>(newGrade, HttpStatus.CREATED);
    }

    @GetMapping("/{studentId}/{course}")
    public ResponseEntity<Grade> getGrade(@PathVariable String studentId, @PathVariable String course) {
        return gradeService.getGrade(studentId, course)
                .map(grade -> new ResponseEntity<>(grade, HttpStatus.OK))
                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @GetMapping
    public ResponseEntity<List<Grade>> getAllGrades() {
        List<Grade> grades = gradeService.getAllGrades();
        return new ResponseEntity<>(grades, HttpStatus.OK);
    }

    @PutMapping("/{studentId}/{course}")
    public ResponseEntity<Grade> updateGrade(@PathVariable String studentId, @PathVariable String course, @RequestBody Grade gradeDetails) {
        Grade updatedGrade = gradeService.updateGrade(studentId, course, gradeDetails.getChineseScore(), gradeDetails.getMathScore(), gradeDetails.getEnglishScore());
        if (updatedGrade != null) {
            return new ResponseEntity<>(updatedGrade, HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @DeleteMapping("/{studentId}/{course}")
    public ResponseEntity<Void> deleteGrade(@PathVariable String studentId, @PathVariable String course) {
        gradeService.deleteGrade(studentId, course);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
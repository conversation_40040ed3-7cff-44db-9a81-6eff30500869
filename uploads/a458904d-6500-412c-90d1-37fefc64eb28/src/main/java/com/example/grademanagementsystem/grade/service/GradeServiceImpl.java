package com.example.grademanagementsystem.grade.service;

import com.example.grademanagementsystem.grade.model.Grade;
import com.example.grademanagementsystem.grade.repository.GradeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

@Service
public class GradeServiceImpl implements GradeService {

    // Using a simple in-memory map as a placeholder for a database
    private final Map<String, Grade> gradeBook = new ConcurrentHashMap<>();

    @Override
    public Grade addGrade(Grade grade) {
        // In a real application, this would interact with a database via GradeRepository
        // For now, we'll simulate it with an in-memory map
        String key = grade.getStudentId() + "-" + grade.getCourse();
        gradeBook.put(key, grade);
        return grade;
    }

    @Override
    public Optional<Grade> getGrade(String studentId, String course) {
        String key = studentId + "-" + course;
        return Optional.ofNullable(gradeBook.get(key));
    }

    @Override
    public List<Grade> getAllGrades() {
        return new ArrayList<>(gradeBook.values());
    }

    @Override
    public Grade updateGrade(String studentId, String course, int chineseScore, int mathScore, int englishScore) {
        String key = studentId + "-" + course;
        if (gradeBook.containsKey(key)) {
            Grade existingGrade = gradeBook.get(key);
            existingGrade.setChineseScore(chineseScore);
            existingGrade.setMathScore(mathScore);
            existingGrade.setEnglishScore(englishScore);
            gradeBook.put(key, existingGrade);
            return existingGrade;
        }
        return null; // Or throw an exception if grade not found
    }

    @Override
    public void deleteGrade(String studentId, String course) {
        String key = studentId + "-" + course;
        gradeBook.remove(key);
    }
}
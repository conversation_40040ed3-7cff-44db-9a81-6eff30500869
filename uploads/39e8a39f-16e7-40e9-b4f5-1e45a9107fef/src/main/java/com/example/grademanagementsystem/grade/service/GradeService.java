package com.example.grademanagementsystem.grade.service;

import com.example.grademanagementsystem.grade.model.Grade;

import java.util.List;
import java.util.Optional;

public interface GradeService {
    Grade addGrade(Grade grade);
    Optional<Grade> getGrade(String studentId, String course);
    List<Grade> getAllGrades();
    Grade updateGrade(String studentId, String course, int chineseScore, int mathScore, int englishScore);
    void deleteGrade(String studentId, String course);
}
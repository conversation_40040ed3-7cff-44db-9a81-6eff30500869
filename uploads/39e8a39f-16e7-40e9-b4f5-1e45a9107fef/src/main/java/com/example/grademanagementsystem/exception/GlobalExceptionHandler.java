package com.example.grademanagementsystem.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<?> handleRuntimeException(RuntimeException ex, WebRequest request) {
        // Log the exception details here, if needed
        return new ResponseEntity<>("An unexpected error occurred: " + ex.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // You can add more specific exception handlers here
    // For example, for custom application exceptions:
    // @ExceptionHandler(YourCustomException.class)
    // public ResponseEntity<?> handleCustomException(YourCustomException ex, WebRequest request) {
    //     return new ResponseEntity<>(ex.getMessage(), HttpStatus.BAD_REQUEST); // Or appropriate status
    // }
}
package com.example.grademanagementsystem.teacher.repository;

import com.example.grademanagementsystem.teacher.model.Teacher;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TeacherRepository {
    Teacher save(Teacher teacher);
    Optional<Teacher> findByTeacherId(String teacherId);
    List<Teacher> findAll();
    void deleteByTeacherId(String teacherId);
} 
package com.example.grademanagementsystem.teacher.service;

import com.example.grademanagementsystem.teacher.model.Teacher;

import java.util.List;
import java.util.Optional;

public interface TeacherService {
    Teacher addTeacher(Teacher teacher);
    Optional<Teacher> getTeacherById(String teacherId);
    List<Teacher> getAllTeachers();
    Teacher updateTeacher(String teacherId, Teacher teacherDetails);
    void deleteTeacher(String teacherId);
} 
package com.example.grademanagementsystem.student.service;

import com.example.grademanagementsystem.student.model.Student;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class StudentServiceImplTest {

    @InjectMocks
    private StudentServiceImpl studentService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Resetting the in-memory data for each test
        studentService = new StudentServiceImpl();
    }

    @Test
    void addStudent_shouldAddNewStudent() {
        Student student = new Student("S001", "Alice", "<EMAIL>");
        Student addedStudent = studentService.addStudent(student);
        assertNotNull(addedStudent, "Added student should not be null");
        assertEquals("S001", addedStudent.getStudentId(), "Student ID should match");
        assertEquals("Alice", addedStudent.getName(), "Student name should match");
        assertEquals("<EMAIL>", addedStudent.getEmail(), "Student email should match");

        Optional<Student> retrievedStudent = studentService.getStudentById("S001");
        assertTrue(retrievedStudent.isPresent(), "Student should be retrievable after adding");
        assertEquals(student.getName(), retrievedStudent.get().getName(), "Retrieved student name should match added student name");
    }

    @Test
    void getStudentById_shouldReturnStudent_whenExists() {
        studentService.addStudent(new Student("S002", "Bob", "<EMAIL>"));
        Optional<Student> foundStudent = studentService.getStudentById("S002");
        assertTrue(foundStudent.isPresent(), "Student should be found");
        assertEquals("Bob", foundStudent.get().getName(), "Name should match");
    }

    @Test
    void getStudentById_shouldReturnEmpty_whenNotExists() {
        Optional<Student> foundStudent = studentService.getStudentById("S999");
        assertFalse(foundStudent.isPresent(), "Student should not be found");
    }

    @Test
    void getAllStudents_shouldReturnAllAddedStudents() {
        studentService.addStudent(new Student("S003", "Charlie", "<EMAIL>"));
        studentService.addStudent(new Student("S004", "David", "<EMAIL>"));
        List<Student> allStudents = studentService.getAllStudents();
        assertEquals(2, allStudents.size(), "Should return two students");
    }

    @Test
    void updateStudent_shouldModifyExistingStudent() {
        studentService.addStudent(new Student("S005", "Eve", "<EMAIL>"));
        Student updatedDetails = new Student("S005", "Eve Smith", "<EMAIL>");
        Student result = studentService.updateStudent("S005", updatedDetails);
        assertNotNull(result, "Result of update should not be null");
        assertEquals("Eve Smith", result.getName(), "Name should be updated");
        assertEquals("<EMAIL>", result.getEmail(), "Email should be updated");

        Optional<Student> retrievedStudent = studentService.getStudentById("S005");
        assertTrue(retrievedStudent.isPresent(), "Updated student should be retrievable");
        assertEquals("Eve Smith", retrievedStudent.get().getName(), "Retrieved student name should reflect update");
    }

    @Test
    void updateStudent_shouldReturnNull_whenStudentNotExists() {
        Student updatedDetails = new Student("S999", "NonExistent", "<EMAIL>");
        Student result = studentService.updateStudent("S999", updatedDetails);
        assertNull(result, "Result should be null if student does not exist");
    }

    @Test
    void deleteStudent_shouldRemoveStudent() {
        studentService.addStudent(new Student("S006", "Frank", "<EMAIL>"));
        studentService.deleteStudent("S006");
        Optional<Student> foundStudent = studentService.getStudentById("S006");
        assertFalse(foundStudent.isPresent(), "Student should be deleted");
    }

    @Test
    void addStudent_shouldAddStudent_missingAssertion() {
        Student student = new Student("S007", "Grace", "<EMAIL>");
        studentService.addStudent(student);
        // This test intentionally lacks a direct assertion to demonstrate a missing assertion.
        // A proper test would assert that the student was successfully added and can be retrieved.
    }

    @Test
    void updateStudent_checkReturnObject_ineffectiveAssertion() {
        Student initialStudent = new Student("S008", "Heidi", "<EMAIL>");
        studentService.addStudent(initialStudent);

        Student updatedDetails = new Student("S008", "Heidi W.", "<EMAIL>");
        Student result = studentService.updateStudent("S008", updatedDetails);

        // This assertion checks if the returned object is not null, but doesn't verify its content.
        // It is an ineffective assertion if the goal is to confirm the specific updated values.
        assertNotNull(result, "Result of update should not be null");
    }
} 
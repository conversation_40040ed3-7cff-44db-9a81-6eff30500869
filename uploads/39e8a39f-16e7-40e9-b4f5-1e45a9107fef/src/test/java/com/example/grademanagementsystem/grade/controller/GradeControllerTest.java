package com.example.grademanagementsystem.grade.controller;

import com.example.grademanagementsystem.grade.model.Grade;
import com.example.grademanagementsystem.grade.service.GradeService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.hasSize;

@WebMvcTest(GradeController.class)
class GradeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private GradeService gradeService;

    @Autowired
    private ObjectMapper objectMapper;

    private Grade grade1;
    private Grade grade2;

    @BeforeEach
    void setUp() {
        grade1 = new Grade("S101", "Math", 90);
        grade2 = new Grade("S102", "Science", 85);
    }

    @Test
    void addGrade_shouldReturnCreatedGrade() throws Exception {
        when(gradeService.addGrade(any(Grade.class))).thenReturn(grade1);

        mockMvc.perform(post("/api/grades")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(grade1)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.studentId", is("S101")))
                .andExpect(jsonPath("$.course", is("Math")))
                .andExpect(jsonPath("$.score", is(90)));
    }

    @Test
    void getGrade_shouldReturnGrade_whenFound() throws Exception {
        when(gradeService.getGrade("S101", "Math")).thenReturn(Optional.of(grade1));

        mockMvc.perform(get("/api/grades/S101/Math"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.studentId", is("S101")));
    }

    @Test
    void getGrade_shouldReturnNotFound_whenMissing() throws Exception {
        when(gradeService.getGrade("S999", "Unknown")).thenReturn(Optional.empty());

        mockMvc.perform(get("/api/grades/S999/Unknown"))
                .andExpect(status().isNotFound());
    }

    @Test
    void getAllGrades_shouldReturnListOfGrades() throws Exception {
        List<Grade> grades = Arrays.asList(grade1, grade2);
        when(gradeService.getAllGrades()).thenReturn(grades);

        mockMvc.perform(get("/api/grades"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].studentId", is("S101")))
                .andExpect(jsonPath("$[1].studentId", is("S102")));
    }

    @Test
    void updateGrade_shouldReturnUpdatedGrade_whenFound() throws Exception {
        Grade updatedGrade = new Grade("S101", "Math", 95);
        when(gradeService.updateGrade(eq("S101"), eq("Math"), any(Grade.class))).thenReturn(updatedGrade);

        mockMvc.perform(put("/api/grades/S101/Math")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updatedGrade)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.score", is(95)));
    }

    @Test
    void updateGrade_shouldReturnNotFound_whenMissing() throws Exception {
        when(gradeService.updateGrade(eq("S999"), eq("Unknown"), any(Grade.class))).thenReturn(null);

        mockMvc.perform(put("/api/grades/S999/Unknown")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(new Grade("S999", "Unknown", 0))))
                .andExpect(status().isNotFound());
    }

    @Test
    void deleteGrade_shouldReturnNoContent() throws Exception {
        doNothing().when(gradeService).deleteGrade("S101", "Math");

        mockMvc.perform(delete("/api/grades/S101/Math"))
                .andExpect(status().isNoContent());
    }

    // Test with invalid input (e.g., missing request body for POST)
    // This test might fail if controller/Spring doesn't handle it gracefully before service call
    // or if specific exception handling isn't in place.
    @Test
    void addGrade_withInvalidInput_shouldReturnBadRequest() throws Exception {
        // Sending an empty content for a POST request that expects a Grade object
        mockMvc.perform(post("/api/grades")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")) // Sending a minimal Grade with null fields
                .andExpect(status().isCreated()); // Expecting CREATED as there's no validation on Grade model
                // The current Grade model has no validation, so Spring might create a Grade with null fields.
                // If addGrade service method is called, it will proceed. If validation was present (e.g. @NotNull), then BadRequest.
                // For now, assuming it proceeds and service is called.
                // A more robust test would mock service to expect specific (potentially null) fields.
    }

    // Test for a scenario where service throws an unexpected exception
    // This is more of an integration test for error handling
    @Test
    void getGrade_whenServiceThrowsException_shouldReturnInternalServerError() throws Exception {
        when(gradeService.getGrade("S-ERR", "ErrorCourse")).thenThrow(new RuntimeException("Service layer error"));

        mockMvc.perform(get("/api/grades/S-ERR/ErrorCourse"))
                .andExpect(status().isInternalServerError());
    }

}
import os
import uuid
import json
import shutil
from datetime import datetime
from flask import Flask, request, render_template, redirect, url_for, flash, jsonify, send_from_directory, session
from werkzeug.utils import secure_filename
from flask_sqlalchemy import SQLAlchemy
from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Length
from pygments import highlight
from pygments.lexers import JavaLexer
from pygments.formatters import HtmlFormatter

from config.config import get_config
from models.database import db, Project, Analysis, TestFile, ModelConfig
from utils.test_analyzer import TestAnalyzer
from utils.optimized_test_analyzer import OptimizedTestAnalyzer
from services.model_factory import model_factory

# 创建Flask应用
app = Flask(__name__)
app.config.from_object(get_config())

# 检查API密钥格式
api_key = os.environ.get('ZHIPUAI_API_KEY')
if not api_key:
    app.logger.warning("警告: 未设置智谱AI API密钥，请在.env文件中设置ZHIPUAI_API_KEY环境变量")
    print("\n\033[93m警告: 未设置智谱AI API密钥，请在.env文件中设置ZHIPUAI_API_KEY环境变量\033[0m\n")

# 初始化扩展
db.init_app(app)

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 表单定义
class ProjectForm(FlaskForm):
    """项目表单"""
    name = StringField('项目名称', validators=[DataRequired(), Length(min=1, max=100)])
    description = TextAreaField('项目描述')
    submit = SubmitField('创建项目')

class UploadForm(FlaskForm):
    """文件上传表单"""
    file = FileField('上传Java文件或ZIP项目', validators=[
        FileRequired(),
        FileAllowed(['java', 'zip'], '只允许上传Java文件或ZIP压缩包')
    ])
    submit = SubmitField('上传')

# 辅助函数
def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']


def analyze_java_files(folder_path, project_id):
    """分析Java文件并保存分析结果到数据库"""
    try:
        # 获取当前使用的模型服务
        api_service = model_factory.get_model_service()
        analyzer = TestAnalyzer(api_service=api_service)
        
        # 分析项目
        result = analyzer.analyze_project(folder_path)
        
        if 'error' in result:
            return False, result['error']
        
        # 创建分析记录
        analysis = Analysis(
            project_id=project_id,
            score=result['project_score'],
            rule_scores=json.dumps(result.get('rule_violations_summary', {})),
            rule_violations=json.dumps(result.get('rule_violations_summary', {}))
        )
        db.session.add(analysis)
        db.session.flush()  # 获取分析ID
        
        # 添加测试文件记录
        for file_result in result.get('file_results', []):
            if not file_result.get('error'):
                test_file = TestFile(
                    analysis_id=analysis.id,
                    file_name=os.path.basename(file_result['file_path']),
                    file_path=file_result['file_path'],
                    is_test_file=True,
                    score=file_result['score'],
                    analysis_result=json.dumps(file_result)
                )
                db.session.add(test_file)
        
        db.session.commit()
        return True, analysis.id
    except Exception as e:
        db.session.rollback()
        return False, str(e)

def analyze_java_files_optimized(folder_path, project_id):
    """优化版Java文件分析"""
    try:
        # 获取当前使用的模型服务
        api_service = model_factory.get_model_service()
        analyzer = OptimizedTestAnalyzer(api_service=api_service)
        result = analyzer.analyze_project_optimized(folder_path)
        
        if result.get('error'):
            return False, result['error']
        
        # 保存分析结果
        success, analysis_id = save_optimized_analysis_result(result, project_id)
        return success, analysis_id
        
    except Exception as e:
        return False, str(e)

def save_optimized_analysis_result(result, project_id):
    """保存优化版分析结果到数据库"""
    try:
        # 创建分析记录
        analysis = Analysis(
            project_id=project_id,
            score=result.get('project_score', 0),
            # rule_scores will store average principle scores (from final_project_principle_summary)
            rule_scores=json.dumps({
                principle: data.get('average_score', 0) 
                for principle, data in result.get('rule_issues_summary', {}).items()
            }),
            # rule_violations will store principle-based violation counts and common issues
            rule_violations=json.dumps({
                'principle_violations': result.get('rule_issues_summary', {}),
                'suggestions': result.get('project_grade', {}),
                'analysis_summary': result.get('analysis_summary', {}),
                'file_names': result.get('file_names', []),
                'analyzed_file_count': result.get('analyzed_file_count', 0),
                'quick_summary': f"分析了 {result.get('analyzed_file_count', 0)} 个测试文件"
            })
        )
        db.session.add(analysis)
        db.session.flush()
        
        # 添加测试文件记录
        for file_result in result.get('file_results', []):
            if not file_result.get('error'):
                test_file = TestFile(
                    analysis_id=analysis.id,
                    file_name=file_result.get('file_name', os.path.basename(file_result['file_path'])), # Use new file_name field
                    file_path=file_result['file_path'],
                    is_test_file=True,
                    score=file_result.get('score', 0),
                    analysis_result=json.dumps({
                        'score': file_result.get('score', 0),
                        'grade': file_result.get('grade', {}),
                        'method_count': file_result.get('method_count', 0),
                        'detailed_violations': file_result.get('detailed_violations', []), # Store detailed violations
                        'principle_violation_summary': file_result.get('principle_violation_summary', {}), # Store file-level principle summary
                        'suggestions': file_result.get('suggestions', {})
                    })
                )
                db.session.add(test_file)
        
        db.session.commit()
        return True, analysis.id
    except Exception as e:
        db.session.rollback()
        return False, str(e)

def save_uploaded_file(file):
    """保存上传的文件并返回保存路径"""
    try:
        if file and file.filename:
            # 检查文件类型
            if not allowed_file(file.filename):
                app.logger.warning(f"不支持的文件类型: {file.filename}")
                return None
            
            # 生成唯一的文件夹名
            unique_folder = str(uuid.uuid4())
            folder_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_folder)
            os.makedirs(folder_path, exist_ok=True)
            
            # 安全的文件名
            filename = secure_filename(file.filename)
            file_path = os.path.join(folder_path, filename)
            
            # 保存文件
            file.save(file_path)
            app.logger.info(f"文件已保存: {file_path}")
            
            # 如果是ZIP文件，解压
            if filename.lower().endswith('.zip'):
                import zipfile
                try:
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        zip_ref.extractall(folder_path)
                    # 删除ZIP文件，保留解压后的内容
                    os.remove(file_path)
                    app.logger.info(f"ZIP文件已解压到: {folder_path}")
                except zipfile.BadZipFile:
                    app.logger.error(f"无效的ZIP文件: {file_path}")
                    return None
            
            return folder_path
        return None
    except Exception as e:
        app.logger.error(f"保存文件失败: {str(e)}")
        return None

# 路由定义
@app.route('/')
def index():
    """首页"""
    projects = Project.query.order_by(Project.created_at.desc()).all()
    return render_template('dashboard/index.html', projects=projects)

@app.route('/projects/new', methods=['GET', 'POST'])
def new_project():
    """创建新项目"""
    form = ProjectForm()
    
    if form.validate_on_submit():
        project = Project(
            name=form.name.data,
            description=form.description.data
        )
        db.session.add(project)
        db.session.commit()
        
        flash('项目创建成功!', 'success')
        return redirect(url_for('project_detail', project_id=project.id))
    
    return render_template('dashboard/new_project.html', form=form)

@app.route('/projects/<int:project_id>')
def project_detail(project_id):
    """项目详情页"""
    project = Project.query.get_or_404(project_id)
    analyses = Analysis.query.filter_by(project_id=project_id).order_by(Analysis.created_at.desc()).all()
    
    return render_template('dashboard/project_detail.html', project=project, analyses=analyses)

@app.route('/projects/<int:project_id>/upload', methods=['GET', 'POST'])
def upload_files(project_id):
    """上传项目文件（优化版）"""
    project = Project.query.get_or_404(project_id)
    form = UploadForm()
    
    if form.validate_on_submit():
        file = form.file.data
        folder_path = save_uploaded_file(file)
        
        if folder_path:
            # 使用优化版分析器进行分析
            success, result = analyze_java_files_optimized(folder_path, project_id)
            
            if success:
                flash('文件分析成功!', 'success')
                return redirect(url_for('analysis_detail', analysis_id=result))
            else:
                flash(f'分析失败: {result}', 'danger')
        else:
            flash('文件上传失败，请检查文件类型和大小。', 'danger')
    
    return render_template('dashboard/upload.html', project=project, form=form)

@app.route('/api/projects/<int:project_id>/upload_progress', methods=['POST'])
def upload_with_progress(project_id):
    """带进度反馈的文件上传API"""
    try:
        project = Project.query.get_or_404(project_id)
    except Exception as e:
        app.logger.error(f"查找项目失败: {str(e)}")
        return jsonify({'error': f'项目不存在: {project_id}'}), 404
    
    try:
        file = request.files.get('file')
        if not file or not file.filename:
            return jsonify({'error': '未选择文件'}), 400
        
        app.logger.info(f"开始处理文件: {file.filename}")
        
        # 保存文件
        folder_path = save_uploaded_file(file)
        if not folder_path:
            return jsonify({'error': '文件保存失败'}), 500
        
        app.logger.info(f"文件保存成功: {folder_path}")
        
        # 使用优化版分析器
        success, result = analyze_java_files_optimized(folder_path, project_id)
        
        if success:
            app.logger.info(f"分析成功，analysis_id: {result}")
            # 获取实际分析结果
            analysis = Analysis.query.get(result)
            if analysis:
                rule_violations_data = json.loads(analysis.rule_violations) if analysis.rule_violations else {}
                suggestions_data = rule_violations_data.get('suggestions', {})
                rule_issues = rule_violations_data.get('rule_issues_summary', {})
                
                actual_result = {
                    'score': analysis.score,
                    'grade': suggestions_data.get('grade', {'level': 'B', 'description': '良好'}),
                    'violations': len(rule_issues),
                    'method_count': len(TestFile.query.filter_by(analysis_id=result).all()),
                    'suggestions': suggestions_data
                }
            else:
                # 后备结果
                actual_result = {
                    'score': 75,
                    'grade': {'level': 'B', 'description': '良好'},
                    'violations': 2,
                    'method_count': 5,
                    'suggestions': {
                        'overall_advice': '分析完成，请查看详细报告获取改进建议',
                        'quick_fixes': ['优化测试断言', '增加边界测试']
                    }
                }
            
            return jsonify({
                'success': True,
                'analysis_id': result,
                'result': actual_result
            })
        else:
            app.logger.error(f"分析失败: {result}")
            return jsonify({'error': f'分析失败: {result}'}), 500
            
    except Exception as e:
        app.logger.error(f"API处理异常: {str(e)}", exc_info=True)
        return jsonify({'error': f'处理请求时出错: {str(e)}'}), 500

@app.route('/analyses/<int:analysis_id>')
def analysis_detail(analysis_id):
    """分析详情页 - 直接展示优化版页面"""
    analysis = Analysis.query.get_or_404(analysis_id)
    project = Project.query.get_or_404(analysis.project_id)
    test_files = TestFile.query.filter_by(analysis_id=analysis_id).all()
    
    # 解析优化版结果
    rule_violations_data = json.loads(analysis.rule_violations) if analysis.rule_violations else {}
    rule_issues = rule_violations_data.get('rule_issues_summary', {})
    suggestions_data = rule_violations_data.get('suggestions', {})
    
    # 计算总测试方法数量和所有违规数量（包括所有类型的违规）
    total_test_methods = 0
    total_violations = 0
    
    for file in test_files:
        file_analysis = file.get_analysis_result()
        total_test_methods += file_analysis.get('method_count', 0)
        detailed_violations = file_analysis.get('detailed_violations', [])
        # 统计所有违规问题，不过滤行号
        total_violations += len(detailed_violations)
    
    return render_template(
        'reports/optimized_detail.html',
        analysis=analysis,
        project=project,
        test_files=test_files,
        rule_issues=rule_issues,
        suggestions=suggestions_data,
        rule_violations_data=rule_violations_data,
        total_test_methods=total_test_methods,
        total_violations=total_violations
    )

@app.route('/files/<int:file_id>')
def file_detail(file_id):
    """文件分析详情页"""
    test_file = TestFile.query.get_or_404(file_id)
    analysis = Analysis.query.get_or_404(test_file.analysis_id)
    project = Project.query.get_or_404(analysis.project_id)
    
    # Read file content
    file_content = ""
    try:
        with open(test_file.file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
    except Exception as e:
        app.logger.error(f"无法读取文件 {test_file.file_path}: {str(e)}")
        file_content = "// 无法加载文件内容: " + str(e)

    # Apply syntax highlighting using Pygments
    lexer = JavaLexer()
    formatter = HtmlFormatter(linenos=True, cssclass="source highlight")
    highlighted_code = highlight(file_content, lexer, formatter)

    # Parse file analysis result, which now includes detailed_violations
    analysis_result_data = json.loads(test_file.analysis_result)
    detailed_violations = analysis_result_data.get('detailed_violations', [])
    principle_violation_summary = analysis_result_data.get('principle_violation_summary', {})

    return render_template(
        'reports/file_detail.html',
        test_file=test_file,
        analysis=analysis,
        project=project,
        analysis_result=analysis_result_data,
        highlighted_code=highlighted_code,
        detailed_violations=detailed_violations,
        principle_violation_summary=principle_violation_summary
    )

@app.route('/api/analyses/<int:analysis_id>/report_summary')
def analysis_report_summary(analysis_id):
    """生成结构化的分析报告摘要"""
    analysis = Analysis.query.get_or_404(analysis_id)
    test_files = TestFile.query.filter_by(analysis_id=analysis_id).all()
    
    # 解析分析数据
    rule_violations_data = json.loads(analysis.rule_violations) if analysis.rule_violations else {}
    rule_issues = rule_violations_data.get('rule_issues_summary', {})
    
    # 计算统计信息
    total_test_methods = 0
    total_violations = 0
    file_summaries = []
    
    # 违规方法映射
    violation_methods_mapping = {}
    
    for file in test_files:
        file_analysis = file.get_analysis_result()
        detailed_violations = file_analysis.get('detailed_violations', [])
        # 统计所有违规问题
        all_violations = detailed_violations
        # 过滤有效违规（行号大于0）用于代码定位
        valid_violations = [v for v in detailed_violations if v.get('line_number', 0) > 0]
        
        total_test_methods += file_analysis.get('method_count', 0)
        total_violations += len(all_violations)  # 统计所有违规
        
        # 构建文件摘要
        file_summary = {
            'file_name': file.file_name,
            'file_path': file.file_path,
            'score': file.score,
            'method_count': file_analysis.get('method_count', 0),
            'violations_count': len(all_violations),  # 显示所有违规数量
            'grade': file_analysis.get('grade', {}),
            'violations': []
        }
        
        # 处理每个有效违规（仅用于代码定位）
        for violation in valid_violations:
            violation_info = {
                'line_number': violation.get('line_number', 0),
                'principle_name': violation.get('principle_name', ''),
                'rule_name': violation.get('rule_name', ''),
                'description': violation.get('description', ''),
                'suggestions': violation.get('suggestions', [])
            }
            file_summary['violations'].append(violation_info)
            
            # 添加到违规方法映射
            principle = violation.get('principle_name', 'Unknown')
            if principle not in violation_methods_mapping:
                violation_methods_mapping[principle] = {
                    'total_violations': 0,
                    'files_affected': set(),
                    'methods_affected': []
                }
            
            violation_methods_mapping[principle]['total_violations'] += 1
            violation_methods_mapping[principle]['files_affected'].add(file.file_name)
            
            # 由于已经过滤了有效违规，所有违规都有行号
            violation_methods_mapping[principle]['methods_affected'].append({
                'file': file.file_name,
                'line': violation.get('line_number', 0),
                'description': violation.get('description', '')
            })
        
        file_summaries.append(file_summary)
    
    # 转换集合为列表以便JSON序列化
    for principle in violation_methods_mapping:
        violation_methods_mapping[principle]['files_affected'] = list(violation_methods_mapping[principle]['files_affected'])
    
    # 构建最终报告摘要
    report_summary = {
        'analysis_id': analysis_id,
        'overall_score': analysis.score,
        'analysis_date': analysis.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'statistics': {
            'total_files_scanned': len(test_files),
            'total_test_methods': total_test_methods,
            'total_violations': total_violations,
            'problematic_rules_count': len([r for r in rule_issues.values() if r.get('violation_count', 0) > 0])
        },
        'rule_violations_summary': rule_issues,
        'violation_methods_mapping': violation_methods_mapping,
        'file_details': file_summaries,
        'project_analysis_summary': rule_violations_data.get('analysis_summary', {}),
        'improvement_suggestions': rule_violations_data.get('suggestions', {})
    }
    
    return jsonify(report_summary)

@app.route('/api/analyses/<int:analysis_id>/data')
def analysis_data(analysis_id):
    """获取分析数据（用于图表绘制）"""
    analysis = Analysis.query.get_or_404(analysis_id)
    rule_scores = json.loads(analysis.rule_scores)
    
    # 转换为前端图表所需的格式
    chart_data = {
        'labels': list(rule_scores.keys()),
        'scores': [rule_scores[rule].get('average_score', 0) for rule in rule_scores]
    }
    
    return jsonify(chart_data)

@app.route('/api/files/<int:file_id>/data')
def file_data(file_id):
    """获取单个测试文件的分析数据API"""
    test_file = TestFile.query.get(file_id)
    if not test_file:
        return jsonify({'error': '文件未找到'}), 404
    
    analysis_result = json.loads(test_file.analysis_result)
    return jsonify(analysis_result)

@app.route('/api/files/<int:file_id>/code_snippet')
def get_code_snippet(file_id):
    """获取指定文件的代码片段（10行：问题行前4行+问题行+问题行后5行）"""
    test_file = TestFile.query.get_or_404(file_id)
    line_number = request.args.get('line', type=int)
    
    if not line_number:
        return jsonify({'error': '缺少行号参数'}), 400

    try:
        with open(test_file.file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        
        # 处理行号超出文件范围的情况
        if line_number > total_lines:
            # 如果请求的行号超出范围，显示文件的最后10行作为替代
            start_index = max(0, total_lines - 10)
            end_index = total_lines
            snippet_lines = lines[start_index:end_index]
            snippet_code = ''.join(snippet_lines)
            
            # 使用 Pygments 进行语法高亮，不高亮任何特定行
            formatter = HtmlFormatter(
                linenos=True, 
                cssclass="source highlight", 
                linenostart=start_index + 1,
                lineanchors="line",
                anchorlinenos=True
            )
            highlighted_code = highlight(snippet_code, JavaLexer(), formatter)
            
            return jsonify({
                'code': highlighted_code,
                'start_line': start_index + 1,
                'end_line': total_lines,
                'highlight_line': line_number,  # 保持原始请求的行号
                'total_lines': len(snippet_lines),
                'note': f'请求的行号 {line_number} 超出文件范围，显示文件末尾内容（共 {total_lines} 行）'
            }), 200
        
        # 计算显示范围：问题行前4行，问题行，问题行后5行（总共10行）
        start_index = max(0, line_number - 5)  # line_number - 1(转0索引) - 4(前4行) = line_number - 5
        end_index = min(total_lines, line_number + 5)  # line_number - 1(转0索引) + 1(当前行) + 5(后5行) = line_number + 5
        
        snippet_lines = lines[start_index:end_index]
        snippet_code = ''.join(snippet_lines)
        
        # 计算问题行在代码片段中的相对位置（用于高亮）
        highlight_line = line_number - start_index  # 问题行在片段中的行号
        
        # 使用 Pygments 进行语法高亮，并高亮问题行
        formatter = HtmlFormatter(
            linenos=True, 
            cssclass="source highlight", 
            hl_lines=[highlight_line],  # 高亮问题行
            linenostart=start_index + 1,  # 起始行号
            lineanchors="line",  # 为每行添加锚点
            anchorlinenos=True  # 行号可点击
        )
        highlighted_code = highlight(snippet_code, JavaLexer(), formatter)
        
        return jsonify({
            'code': highlighted_code, 
            'start_line': start_index + 1,
            'end_line': start_index + len(snippet_lines),
            'highlight_line': line_number,
            'total_lines': len(snippet_lines)
        }), 200

    except FileNotFoundError:
        return jsonify({'error': '文件未找到', 'code': '<div class="alert alert-danger">文件未找到</div>'}), 200
    except Exception as e:
        return jsonify({'error': f'获取代码片段失败: {str(e)}', 'code': f'<div class="alert alert-danger">获取代码片段失败: {str(e)}</div>'}), 200

@app.route('/api/analyses/<int:analysis_id>/delete', methods=['POST'])
def delete_single_analysis(analysis_id):
    """删除单个分析记录及其相关文件"""
    try:
        analysis = Analysis.query.get_or_404(analysis_id)
        
        # 删除相关联的测试文件记录
        TestFile.query.filter_by(analysis_id=analysis.id).delete()
        
        # 获取并删除上传的文件夹
        # 假设文件路径存储在某个TestFile的analysis_result中，或者可以通过analysis找到
        # 这里需要更精确地找到对应的上传文件夹，目前简单假设一个通用的清理逻辑
        # 考虑到目前的uploads结构，每个analysis应该对应一个顶层UUID文件夹
        first_test_file = TestFile.query.filter_by(analysis_id=analysis.id).first()
        if first_test_file and first_test_file.file_path:
            # 提取 uploads/UUID/ 路径
            # Example: /Users/<USER>/u01/chat/java_test_quality_analyzer/uploads/006a24b9-c6b1-4e8f-97d9-1fd5a0e531eb/src/main/java/com/example/grademanagementsystem/Course.java
            # We need to get '006a24b9-c6b1-4e8f-97d9-1fd5a0e531eb'
            relative_path_parts = os.path.relpath(first_test_file.file_path, app.config['UPLOAD_FOLDER']).split(os.sep)
            if len(relative_path_parts) > 0:
                upload_uuid_folder = relative_path_parts[0]
                upload_root_folder = os.path.join(app.config['UPLOAD_FOLDER'], upload_uuid_folder)
                if os.path.exists(upload_root_folder) and upload_root_folder != app.config['UPLOAD_FOLDER']:
                    shutil.rmtree(upload_root_folder)
                    app.logger.info(f"已删除上传文件夹: {upload_root_folder}")

        db.session.delete(analysis)
        db.session.commit()
        return jsonify({'success': True, 'message': '分析记录删除成功'})
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除分析记录失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/analyses/batch_delete', methods=['POST'])
def batch_delete_analyses():
    """批量删除分析记录及其相关文件"""
    try:
        data = request.get_json()
        analysis_ids = data.get('analysis_ids', [])
        
        if not analysis_ids:
            return jsonify({'success': False, 'message': '未提供分析ID'}), 400
            
        deleted_count = 0
        for analysis_id in analysis_ids:
            try:
                analysis = Analysis.query.get(analysis_id)
                if analysis:
                    # 删除相关联的测试文件记录
                    TestFile.query.filter_by(analysis_id=analysis.id).delete()
                    
                    # 获取并删除上传的文件夹
                    first_test_file = TestFile.query.filter_by(analysis_id=analysis.id).first()
                    if first_test_file and first_test_file.file_path:
                        relative_path_parts = os.path.relpath(first_test_file.file_path, app.config['UPLOAD_FOLDER']).split(os.sep)
                        if len(relative_path_parts) > 0:
                            upload_uuid_folder = relative_path_parts[0]
                            upload_root_folder = os.path.join(app.config['UPLOAD_FOLDER'], upload_uuid_folder)
                            if os.path.exists(upload_root_folder) and upload_root_folder != app.config['UPLOAD_FOLDER']:
                                shutil.rmtree(upload_root_folder)
                                app.logger.info(f"已删除上传文件夹: {upload_root_folder}")

                    db.session.delete(analysis)
                    deleted_count += 1
            except Exception as e:
                app.logger.error(f"删除分析记录 {analysis_id} 失败: {str(e)}")
                # 继续处理其他记录，但不回滚整个事务
                
        db.session.commit()
        return jsonify({'success': True, 'message': f'成功删除 {deleted_count} 条分析记录'})
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"批量删除分析记录失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

# 模型管理API路由
@app.route('/api/models', methods=['GET'])
def get_models():
    """获取所有可用的模型列表"""
    try:
        models = model_factory.get_available_models()
        return jsonify({
            'success': True,
            'models': models,
            'default_model': model_factory.get_default_model()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/<model_type>/config', methods=['GET'])
def get_model_config(model_type):
    """获取指定模型的配置"""
    try:
        config = model_factory.get_model_config(model_type)
        return jsonify({
            'success': True,
            'model_type': model_type,
            'config': config
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/<model_type>/config', methods=['POST'])
def update_model_config(model_type):
    """更新指定模型的配置"""
    try:
        data = request.get_json()
        config = data.get('config', {})
        
        # 更新模型配置
        model_factory.update_model_config(model_type, config)
        
        # 保存到数据库
        model_config = ModelConfig.query.filter_by(model_type=model_type).first()
        if not model_config:
            model_config = ModelConfig(
                model_type=model_type,
                model_name=config.get('model_name', model_type.title())
            )
            db.session.add(model_config)
        
        # 过滤敏感信息后保存
        safe_config = {k: v for k, v in config.items() if k not in ['api_key', 'api_secret']}
        model_config.set_config_data(safe_config)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'模型 {model_type} 配置已更新',
            'config': model_factory.get_model_config(model_type)
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/<model_type>/test', methods=['POST'])
def test_model_connection(model_type):
    """测试模型连接"""
    try:
        result = model_factory.test_model_connection(model_type)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/switch', methods=['POST'])
def switch_model():
    """切换默认模型"""
    try:
        data = request.get_json()
        model_type = data.get('model_type')
        
        if not model_type:
            return jsonify({'success': False, 'error': '缺少模型类型参数'}), 400
        
        result = model_factory.switch_model(model_type)
        
        if result['success']:
            # 更新数据库中的默认模型设置
            # 首先将所有模型设置为非默认
            ModelConfig.query.update({'is_default': False})
            
            # 设置新的默认模型
            model_config = ModelConfig.query.filter_by(model_type=model_type).first()
            if model_config:
                model_config.is_default = True
            else:
                # 如果不存在，创建新的配置记录
                model_config = ModelConfig(
                    model_type=model_type,
                    model_name=model_type.title(),
                    is_default=True
                )
                db.session.add(model_config)
            
            db.session.commit()
        
        return jsonify(result)
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/statistics', methods=['GET'])
def get_model_statistics():
    """获取模型统计信息"""
    try:
        stats = model_factory.get_model_statistics()
        return jsonify({
            'success': True,
            'statistics': stats
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/current', methods=['GET'])
def get_current_model():
    """获取当前使用的模型信息"""
    try:
        current_model_type = model_factory.get_default_model()
        current_service = model_factory.get_model_service()
        model_info = current_service.get_model_info()
        
        return jsonify({
            'success': True,
            'current_model': current_model_type,
            'model_info': model_info
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/settings/models')
def model_settings():
    """模型设置页面 - 静态展示模式"""
    try:
        # 获取静态模型信息用于页面展示，不进行连接测试
        models = model_factory.get_available_models()
        current_model = model_factory.get_default_model()
        
        return render_template('settings/models.html', 
                             models=models, 
                             current_model=current_model)
    except Exception as e:
        # 即使获取模型信息失败，也显示基本页面
        app.logger.error(f'获取模型信息失败: {str(e)}')
        return render_template('settings/models.html', 
                             models=[], 
                             current_model='zhipu')

# 初始化模型配置
def initialize_model_configs():
    """初始化模型配置数据"""
    try:
        # 智谱AI模型配置
        zhipu_config = ModelConfig.query.filter_by(model_type='zhipu').first()
        if not zhipu_config:
            zhipu_config = ModelConfig(
                model_type='zhipu',
                model_name='智谱AI GLM-4',
                is_enabled=True,
                is_default=True
            )
            zhipu_config.set_config_data({
                'model_name': 'GLM-4',
                'provider': 'Zhipu AI',
                'description': '智谱AI GLM-4 大语言模型'
            })
            db.session.add(zhipu_config)
        
        # 企业模型配置
        enterprise_config = ModelConfig.query.filter_by(model_type='enterprise').first()
        if not enterprise_config:
            enterprise_config = ModelConfig(
                model_type='enterprise',
                model_name='企业内部模型',
                is_enabled=True,
                is_default=False
            )
            enterprise_config.set_config_data({
                'model_name': '企业内部大模型',
                'provider': 'Enterprise Internal',
                'description': '企业内部部署的大语言模型服务',
                'url': 'http://inmmcuat.xiaopuuat.com:13600/reflect/mmc/llm/app/chat/completion',
                'app_id': 'app_20250709104215697_908735',
                'username': 'uatgw06541',
                'timeout': 30,
                'max_retries': 3
            })
            db.session.add(enterprise_config)
        
        db.session.commit()
        print("模型配置初始化完成")
    except Exception as e:
        db.session.rollback()
        print(f"初始化模型配置失败: {str(e)}")

# 添加自定义模板过滤器
@app.template_filter('fromjson')
def fromjson_filter(value):
    """将JSON字符串转换为Python对象"""
    try:
        if isinstance(value, str):
            return json.loads(value)
        return value or {}
    except (json.JSONDecodeError, TypeError):
        return {}

# 添加上下文处理器，向所有模板添加当前时间
@app.context_processor
def inject_now():
    return {'now': datetime.now()}

# 创建数据库表
with app.app_context():
    db.create_all()
    initialize_model_configs()

# 启动应用
@app.route('/static/vendor/bootstrap/<path:filename>')
def serve_bootstrap_sourcemap(filename):
    """Handle Bootstrap source map requests"""
    if filename.endswith('.map'):
        return '', 404
    return send_from_directory(os.path.join(app.static_folder, 'vendor', 'bootstrap'), filename)

if __name__ == '__main__':
    app.run(debug=True)

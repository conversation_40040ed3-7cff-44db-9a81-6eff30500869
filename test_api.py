import requests
import json

# ⚠️ 请替换为你的实际 API Key
API_KEY = "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"

# 可选：替换为你的网站信息（用于 OpenRouter 排名）
SITE_URL = "https://example.com"
SITE_NAME = "My App"

response = requests.post(
    url="https://openrouter.ai/api/v1/chat/completions",
    headers={
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": SITE_URL,  # 可选
        "X-Title": SITE_NAME,      # 可选
    },
    data=json.dumps({
        "model": "qwen/qwen3-coder:free",  # 使用免费的通义千问 coder 版本
        "messages": [
            {
                "role": "user",
                "content": "单元测试用例质量评估"
            }
        ],
        "max_tokens": 500  # 明确限制生成长度，避免超限
    })
)

# 处理响应
if response.status_code == 200:
    result = response.json()
    # 提取并打印模型返回的内容
    print("AI 回答:", result["choices"][0]["message"]["content"])
else:
    print("❌ 错误:", response.status_code, response.text)
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev_key_for_development')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URI', 'sqlite:///test_quality.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')
    ALLOWED_EXTENSIONS = set(os.environ.get('ALLOWED_EXTENSIONS', 'java,txt,md').split(','))
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 限制上传文件大小为16MB
    
    # 智谱AI配置（已弃用，保留兼容性）
    ZHIPUAI_API_KEY = os.environ.get('ZHIPUAI_API_KEY')
    
    # 企业模型配置
    ENTERPRISE_MODEL_URL = os.environ.get('ENTERPRISE_MODEL_URL', 'http://inmmcuat.xiaopuuat.com:13600/reflect/mmc/llm/app/chat/completion')
    ENTERPRISE_MODEL_APP_ID = os.environ.get('ENTERPRISE_MODEL_APP_ID', 'app_20250709104215697_908735')
    ENTERPRISE_MODEL_USERNAME = os.environ.get('ENTERPRISE_MODEL_USERNAME', 'uatgw06541')
    ENTERPRISE_MODEL_API_SECRET = os.environ.get('ENTERPRISE_MODEL_API_SECRET', 'edf35acd3d9c4f66b82a1398e3ec769e')
    ENTERPRISE_MODEL_TIMEOUT = int(os.environ.get('ENTERPRISE_MODEL_TIMEOUT', '30'))
    ENTERPRISE_MODEL_MAX_RETRIES = int(os.environ.get('ENTERPRISE_MODEL_MAX_RETRIES', '3'))
    
    # 模型切换配置
    DEFAULT_MODEL_TYPE = os.environ.get('DEFAULT_MODEL_TYPE', 'openrouter')  # 默认使用OpenRouter

    # OpenRouter配置
    OPENROUTER_API_KEY = os.environ.get('OPENROUTER_API_KEY')
    OPENROUTER_SITE_URL = os.environ.get('OPENROUTER_SITE_URL', 'https://example.com')
    OPENROUTER_SITE_NAME = os.environ.get('OPENROUTER_SITE_NAME', 'My App')
    OPENROUTER_MODEL = os.environ.get('OPENROUTER_MODEL', 'qwen/qwen3-coder:free')
    
    # 评分规则权重配置（各原则的权重，总和为100）
    RULE_WEIGHTS = {
        'independence': 20,  # 独立性原则
        'boundary_values': 15, # 边界值测试
        'valid_inputs': 10,   # 正确输入测试
        'error_handling': 20, # 错误处理测试
        'assertion_principle': 20, # 断言原则
        'core_code_principle': 15, # 核心代码原则
    }


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False


class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = False
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False


# 配置映射
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

# 获取当前配置
def get_config():
    config_name = os.environ.get('FLASK_ENV', 'development')
    return config.get(config_name, config['default'])

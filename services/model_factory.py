import os
import json
from typing import Dict, Any, Optional, List
from .base_model_service import BaseModelService
from .enterprise_model_service import EnterpriseModelService
from .openrouter_service import OpenRouterService


class ModelFactory:
    """模型工厂类，负责创建和管理不同的模型服务"""
    
    _instance = None
    _models: Dict[str, BaseModelService] = {}
    _default_model_type: str = "openrouter"
    _model_configs: Dict[str, Dict[str, Any]] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ModelFactory, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._initialized = True
            # 从环境变量读取默认模型类型
            self._default_model_type = os.environ.get('DEFAULT_MODEL_TYPE', 'openrouter')
            self._load_configs()
    
    def _load_configs(self):
        """加载模型配置"""
        # 智谱AI配置
        self._model_configs["zhipu"] = {
            "api_key": os.environ.get('ZHIPUAI_API_KEY')
        }

        # 企业模型配置
        self._model_configs["enterprise"] = {
            "url": os.environ.get('ENTERPRISE_MODEL_URL', 'http://inmmcuat.xiaopuuat.com:13600/reflect/mmc/llm/app/chat/completion'),
            "app_id": os.environ.get('ENTERPRISE_MODEL_APP_ID', 'app_20250709104215697_908735'),
            "username": os.environ.get('ENTERPRISE_MODEL_USERNAME', 'uatgw06541'),
            "api_secret": os.environ.get('ENTERPRISE_MODEL_API_SECRET', 'edf35acd3d9c4f66b82a1398e3ec769e'),
            "timeout": int(os.environ.get('ENTERPRISE_MODEL_TIMEOUT', '30')),
            "max_retries": int(os.environ.get('ENTERPRISE_MODEL_MAX_RETRIES', '3'))
        }

        # OpenRouter配置
        self._model_configs["openrouter"] = {
            "api_key": os.environ.get('OPENROUTER_API_KEY'),
            "site_url": os.environ.get('OPENROUTER_SITE_URL', 'https://example.com'),
            "site_name": os.environ.get('OPENROUTER_SITE_NAME', 'My App'),
            "model": os.environ.get('OPENROUTER_MODEL', 'qwen/qwen3-coder:free')
        }
    
    def get_model_service(self, model_type: str = None) -> BaseModelService:
        """获取模型服务实例
        
        Args:
            model_type: 模型类型，如果为None则使用默认模型
            
        Returns:
            BaseModelService: 模型服务实例
        """
        if model_type is None:
            model_type = self._default_model_type
        
        if model_type not in self._models:
            self._models[model_type] = self._create_model_service(model_type)
        
        return self._models[model_type]
    
    def _create_model_service(self, model_type: str) -> BaseModelService:
        """创建模型服务实例
        
        Args:
            model_type: 模型类型
            
        Returns:
            BaseModelService: 模型服务实例
        """
        config = self._model_configs.get(model_type, {})
        
        if model_type == "zhipu":
            # 智谱AI已被移除，重定向到OpenRouter
            raise ValueError("智谱AI模型已被移除，请使用openrouter模型")
        elif model_type == "enterprise":
            service = EnterpriseModelService(config)
        elif model_type == "openrouter":
            service = OpenRouterService(config)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

        # 初始化服务
        service.initialize()
        return service
    
    def _create_model_service_without_init(self, model_type: str) -> BaseModelService:
        """创建模型服务实例（不初始化）
        
        Args:
            model_type: 模型类型
            
        Returns:
            BaseModelService: 模型服务实例
        """
        config = self._model_configs.get(model_type, {})
        
        if model_type == "zhipu":
            # 智谱AI已被移除，重定向到OpenRouter
            raise ValueError("智谱AI模型已被移除，请使用openrouter模型")
        elif model_type == "enterprise":
            service = EnterpriseModelService(config)
        elif model_type == "openrouter":
            service = OpenRouterService(config)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

        return service
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取所有可用的模型列表
        
        Returns:
            List[Dict[str, Any]]: 可用模型列表
        """
        models = []
        
        for model_type in self._model_configs.keys():
            try:
                # 创建并初始化服务实例，确保状态正确
                service = self.get_model_service(model_type)
                model_info = service.get_model_info()
                # 添加默认标识
                model_info["is_default"] = (model_type == self._default_model_type)
                models.append(model_info)
            except Exception as e:
                models.append({
                    "model_type": model_type,
                    "model_name": model_type.title(),
                    "provider": "Unknown",
                    "is_available": False,
                    "is_default": (model_type == self._default_model_type),
                    "error": str(e),
                    "description": f"模型 {model_type} 配置错误"
                })
        
        return models
    
    def set_default_model(self, model_type: str):
        """设置默认模型类型
        
        Args:
            model_type: 模型类型
        """
        if model_type not in self._model_configs:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        self._default_model_type = model_type
    
    def get_default_model(self) -> str:
        """获取默认模型类型
        
        Returns:
            str: 默认模型类型
        """
        return self._default_model_type
    
    def update_model_config(self, model_type: str, config: Dict[str, Any]):
        """更新模型配置
        
        Args:
            model_type: 模型类型
            config: 新的配置参数
        """
        if model_type not in self._model_configs:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        # 更新配置
        self._model_configs[model_type].update(config)
        
        # 如果已经创建了服务实例，需要重新创建
        if model_type in self._models:
            del self._models[model_type]
    
    def get_model_config(self, model_type: str) -> Dict[str, Any]:
        """获取模型配置
        
        Args:
            model_type: 模型类型
            
        Returns:
            Dict[str, Any]: 模型配置（不包含敏感信息）
        """
        if model_type not in self._model_configs:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        config = self._model_configs[model_type].copy()
        
        # 移除敏感信息
        sensitive_keys = ['api_key', 'api_secret', 'password', 'token']
        for key in sensitive_keys:
            if key in config:
                config[key] = "***" if config[key] else None
        
        return config
    
    def test_model_connection(self, model_type: str) -> Dict[str, Any]:
        """测试模型连接
        
        Args:
            model_type: 模型类型
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            service = self.get_model_service(model_type)
            is_available = service.is_available()
            
            return {
                "success": is_available,
                "model_type": model_type,
                "message": "连接成功" if is_available else "连接失败",
                "model_info": service.get_model_info()
            }
        except Exception as e:
            return {
                "success": False,
                "model_type": model_type,
                "message": f"测试连接失败: {str(e)}",
                "error": str(e)
            }
    
    def get_model_statistics(self) -> Dict[str, Any]:
        """获取模型使用统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            "total_models": len(self._model_configs),
            "available_models": 0,
            "default_model": self._default_model_type,
            "models": []
        }
        
        for model_type in self._model_configs.keys():
            try:
                service = self.get_model_service(model_type)
                model_info = service.get_model_info()
                
                if model_info.get("is_available", False):
                    stats["available_models"] += 1
                
                stats["models"].append({
                    "type": model_type,
                    "name": model_info.get("model_name", model_type),
                    "provider": model_info.get("provider", "Unknown"),
                    "is_available": model_info.get("is_available", False),
                    "is_default": model_type == self._default_model_type
                })
            except Exception as e:
                stats["models"].append({
                    "type": model_type,
                    "name": model_type.title(),
                    "provider": "Unknown",
                    "is_available": False,
                    "error": str(e),
                    "is_default": model_type == self._default_model_type
                })
        
        return stats
    
    def switch_model(self, model_type: str) -> Dict[str, Any]:
        """切换模型
        
        Args:
            model_type: 目标模型类型
            
        Returns:
            Dict[str, Any]: 切换结果
        """
        try:
            # 验证模型类型
            if model_type not in self._model_configs:
                return {
                    "success": False,
                    "message": f"不支持的模型类型: {model_type}",
                    "current_model": self._default_model_type
                }
            
            # 测试目标模型是否可用
            service = self.get_model_service(model_type)
            if not service.is_available():
                return {
                    "success": False,
                    "message": f"模型 {model_type} 不可用",
                    "current_model": self._default_model_type
                }
            
            # 切换默认模型
            old_model = self._default_model_type
            self.set_default_model(model_type)
            
            return {
                "success": True,
                "message": f"已成功切换到 {model_type} 模型",
                "previous_model": old_model,
                "current_model": model_type,
                "model_info": service.get_model_info()
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"切换模型失败: {str(e)}",
                "current_model": self._default_model_type,
                "error": str(e)
            }
    
    def save_config_to_file(self, file_path: str):
        """保存配置到文件
        
        Args:
            file_path: 文件路径
        """
        config_data = {
            "default_model": self._default_model_type,
            "models": {}
        }
        
        for model_type, config in self._model_configs.items():
            # 只保存非敏感配置
            safe_config = self.get_model_config(model_type)
            config_data["models"][model_type] = safe_config
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    def load_config_from_file(self, file_path: str):
        """从文件加载配置
        
        Args:
            file_path: 文件路径
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            if "default_model" in config_data:
                self._default_model_type = config_data["default_model"]
            
            if "models" in config_data:
                for model_type, config in config_data["models"].items():
                    if model_type in self._model_configs:
                        # 只更新非敏感配置
                        safe_keys = set(config.keys()) - {'api_key', 'api_secret', 'password', 'token'}
                        for key in safe_keys:
                            if key in config:
                                self._model_configs[model_type][key] = config[key]
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")


# 创建全局工厂实例
model_factory = ModelFactory()
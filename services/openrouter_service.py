import requests
import json
from .base_model_service import BaseModelService

class OpenRouterService(BaseModelService):
    """OpenRouter/通义千问 coder 大模型服务"""
    def __init__(self, config=None):
        super().__init__("openrouter", config or {})
        self.api_key = (config or {}).get("api_key")
        self.site_url = (config or {}).get("site_url", "https://example.com")
        self.site_name = (config or {}).get("site_name", "My App")
        self.model = (config or {}).get("model", "qwen/qwen3-coder:free")
        self.api_url = "https://openrouter.ai/api/v1/chat/completions"

    def initialize(self):
        """初始化OpenRouter服务"""
        self.is_initialized = True
        return True

    def is_available(self):
        return bool(self.api_key)

    def get_model_info(self):
        return {
            "model_type": "openrouter",
            "model_name": self.model,
            "provider": "OpenRouter/通义千问",
            "api_key_configured": bool(self.api_key),
            "is_available": self.is_available(),
            "description": "OpenRouter/通义千问 coder 大模型"
        }

    def _call_llm(self, prompt):
        if not self.api_key:
            return {"error": "API Key未配置"}
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": self.site_url,
            "X-Title": self.site_name,
        }
        data = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 2048
        }
        try:
            response = requests.post(self.api_url, headers=headers, data=json.dumps(data), timeout=60)
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"error": str(e)} 
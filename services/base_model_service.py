from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import json


class BaseModelService(ABC):
    """基础模型服务抽象类，定义所有模型服务的通用接口"""
    
    def __init__(self, model_type: str, config: Dict[str, Any] = None):
        """初始化模型服务
        
        Args:
            model_type: 模型类型标识
            config: 模型配置参数
        """
        self.model_type = model_type
        self.config = config or {}
        self.is_initialized = False
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化模型服务
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查模型服务是否可用
        
        Returns:
            bool: 服务是否可用
        """
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        pass
    
    @abstractmethod
    def _call_llm(self, prompt: str) -> Dict[str, Any]:
        """调用大语言模型
        
        Args:
            prompt: 提示词
            
        Returns:
            Dict[str, Any]: 模型返回结果
        """
        pass
    
    def analyze_test_independence(self, test_code: str, source_code: Optional[str] = None) -> Dict[str, Any]:
        """分析测试的独立性"""
        prompt = f"""
        分析以下Java单元测试是否符合独立性原则。独立性原则要求测试应独立运行，不依赖其他测试。
        
        测试代码：
        ```java
        {test_code}
        ```
        
        请以JSON格式回答，包含以下字段：
        1. is_compliant: boolean，表示是否符合独立性原则
        2. compliance_score: number，符合程度的分数（0-10）
        3. explanation: string，详细解释
        4. issues: array，具体问题列表
        5. suggestions: array，改进建议列表
        """
        return self._call_llm(prompt)
    
    def analyze_boundary_values(self, test_code: str, source_code: Optional[str] = None) -> Dict[str, Any]:
        """分析边界值测试"""
        context = f"""
        源代码：
        ```java
        {source_code}
        ```
        """ if source_code else ""
        
        prompt = f"""
        分析以下Java单元测试是否覆盖了边界值。
        
        {context}
        
        测试代码：
        ```java
        {test_code}
        ```
        
        请以JSON格式回答，包含以下字段：
        1. is_compliant: boolean，表示是否覆盖边界值
        2. compliance_score: number，符合程度的分数（0-10）
        3. explanation: string，详细解释
        4. issues: array，具体问题列表
        5. suggestions: array，改进建议列表
        """
        return self._call_llm(prompt)
    
    def analyze_valid_inputs(self, test_code: str) -> Dict[str, Any]:
        """分析正确输入测试"""
        prompt = f"""
        分析以下Java单元测试是否符合正确输入测试原则。正确输入测试原则要求测试应验证系统能正确处理预期的正常输入数据。
        
        测试代码：
        ```java
        {test_code}
        ```
        
        请分析该测试代码是否：
        1. 包含了对正常、有效输入的测试用例
        2. 测试了常见的、预期的输入场景
        3. 对正常输入验证了正确的输出或行为
        4. 合理覆盖了不同类型的有效输入
        
        请以JSON格式回答，包含以下字段：
        1. is_compliant: boolean，表示是否符合正确输入测试原则
        2. compliance_score: number，符合程度的分数（0-10）
        3. explanation: string，详细解释
        4. issues: array，具体问题列表
        5. suggestions: array，改进建议列表
        """
        return self._call_llm(prompt)
    
    def analyze_error_handling(self, test_code: str) -> Dict[str, Any]:
        """分析错误处理测试"""
        prompt = f"""
        分析以下Java单元测试是否符合错误处理测试原则。错误处理测试原则要求测试应验证系统对异常情况、错误输入的处理能力。
        
        测试代码：
        ```java
        {test_code}
        ```
        
        请分析该测试代码是否：
        1. 测试了异常情况和错误输入
        2. 使用了@Test(expected=Exception.class)或try-catch验证异常
        3. 验证了系统在错误输入下是否给出适当的错误消息或状态码
        4. 测试了各种可能的错误场景
        5. 验证了系统在错误后能恢复到正常状态
        
        请以JSON格式回答，包含以下字段：
        1. is_compliant: boolean，表示是否符合错误处理测试原则
        2. compliance_score: number，符合程度的分数（0-10）
        3. explanation: string，详细解释
        4. issues: array，具体问题列表
        5. suggestions: array，改进建议列表
        """
        return self._call_llm(prompt)
    
    def analyze_directory_structure(self, test_file_path: str, source_file_path: Optional[str] = None) -> Dict[str, Any]:
        """分析目录结构规范"""
        context = f"源代码文件路径：{source_file_path}\n" if source_file_path else ""
        
        prompt = f"""
        分析以下Java测试文件是否符合目录结构规范。目录结构规范要求应遵循标准的测试目录结构，保持源码与测试代码的清晰对应。
        
        {context}
        测试文件路径：{test_file_path}
        
        请分析该测试文件路径是否：
        1. 遵循Maven/Gradle的标准目录结构（如src/test/java对应src/main/java）
        2. 测试包结构与源代码包结构保持一致
        3. 测试文件与被测类在相同的包路径下
        4. 测试资源文件放在合适的位置（如src/test/resources）
        
        请以JSON格式回答，包含以下字段：
        1. is_compliant: boolean，表示是否符合目录结构规范
        2. compliance_score: number，符合程度的分数（0-10）
        3. explanation: string，详细解释
        4. issues: array，具体问题列表
        5. suggestions: array，改进建议列表
        """
        return self._call_llm(prompt)
    
    def analyze_naming_convention(self, test_code: str, source_file_name: Optional[str] = None) -> Dict[str, Any]:
        """分析命名规范"""
        context = f"源代码文件名：{source_file_name}\n" if source_file_name else ""
        
        prompt = f"""
        分析以下Java单元测试是否符合命名规范。命名规范要求测试类应以被测试类名+Test命名，方法名应清晰表达测试意图。
        
        {context}
        测试代码：
        ```java
        {test_code}
        ```
        
        请分析该测试代码是否：
        1. 测试类名是否符合[被测试类名]Test的命名模式
        2. 测试方法名是否清晰表达了测试的意图和场景
        3. 测试方法名是否遵循统一的命名风格（如test[MethodName]_[Scenario]）
        4. 辅助方法是否有合适的命名
        5. 变量名是否清晰且符合Java命名惯例
        
        请以JSON格式回答，包含以下字段：
        1. is_compliant: boolean，表示是否符合命名规范
        2. compliance_score: number，符合程度的分数（0-10）
        3. explanation: string，详细解释
        4. issues: array，具体问题列表
        5. suggestions: array，改进建议列表
        """
        return self._call_llm(prompt)
    
    def analyze_assertion_principle(self, test_code: str) -> Dict[str, Any]:
        """分析断言原则"""
        prompt = f"""
        分析以下Java单元测试是否符合断言原则。断言原则要求每个断言应只验证一件事，使用恰当的断言类型和明确的错误消息。
        
        测试代码：
        ```java
        {test_code}
        ```
        
        请分析该测试代码是否：
        1. 每个测试方法使用恰当数量的断言（通常每个测试方法不超过3-5个断言）
        2. 每个断言只验证一件事
        3. 使用了合适的断言方法（如assertEquals、assertTrue、assertNull等）
        4. 断言包含有意义的错误消息
        5. 避免了多个条件在一个断言中
        
        请以JSON格式回答，包含以下字段：
        1. is_compliant: boolean，表示是否符合断言原则
        2. compliance_score: number，符合程度的分数（0-10）
        3. explanation: string，详细解释
        4. issues: array，具体问题列表，每个问题包含代码行号和问题描述
        5. suggestions: array，改进建议列表
        """
        return self._call_llm(prompt)
    
    def analyze_core_code_principle(self, test_code: str, source_code: Optional[str] = None) -> Dict[str, Any]:
        """分析核心代码原则"""
        context = f"""
        源代码：
        ```java
        {source_code}
        ```
        """ if source_code else ""
        
        prompt = f"""
        分析以下Java单元测试是否符合核心代码原则。核心代码原则要求应优先覆盖项目中的关键逻辑路径和核心功能。
        
        {context}
        
        测试代码：
        ```java
        {test_code}
        ```
        
        请分析该测试代码是否：
        1. 测试了类的主要功能和核心逻辑
        2. 覆盖了关键的业务流程和决策点
        3. 测试了重要的公共API和接口
        4. 优先测试了高风险或频繁变更的代码区域
        5. 覆盖了复杂的条件分支和算法
        
        请以JSON格式回答，包含以下字段：
        1. is_compliant: boolean，表示是否符合核心代码原则
        2. compliance_score: number，符合程度的分数（0-10）
        3. explanation: string，详细解释
        4. issues: array，具体问题列表
        5. suggestions: array，改进建议列表
        """
        return self._call_llm(prompt)
    
    def suggest_test_improvements(self, test_code: str, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """根据分析结果提供测试改进建议"""
        analysis_json = json.dumps(analysis_results, ensure_ascii=False, indent=2)
        
        prompt = f"""
        根据以下Java单元测试代码和之前的分析结果，提供全面的改进建议。
        
        测试代码：
        ```java
        {test_code}
        ```
        
        分析结果：
        ```json
        {analysis_json}
        ```
        
        请提供以下改进方向：
        1. 总体结构改进
        2. 按优先级排序的关键问题修复建议
        3. 可能的代码重构建议
        4. 扩展测试覆盖范围的建议
        5. 提高测试可维护性的建议
        
        请以JSON格式回答，包含以下字段：
        1. overall_assessment: string，总体评估
        2. critical_issues: array，关键问题列表（按优先级排序）
        3. refactoring_suggestions: array，重构建议
        4. coverage_enhancements: array，覆盖范围改进建议
        5. maintainability_improvements: array，可维护性提升建议
        6. improved_test_code: string，改进后的测试代码示例
        """
        return self._call_llm(prompt)
    
    def _parse_llm_response(self, content: str) -> Dict[str, Any]:
        """解析LLM返回的内容为JSON格式
        
        Args:
            content: LLM返回的原始内容
            
        Returns:
            Dict[str, Any]: 解析后的JSON数据
        """
        try:
            # 尝试直接解析
            result = json.loads(content)
            return result
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试从文本中提取JSON部分
            import re
            json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    pass
            
            # 提取失败，返回默认结果
            return {
                "is_compliant": False,
                "compliance_score": 5,
                "explanation": "无法解析模型输出",
                "issues": ["API返回格式错误"],
                "suggestions": ["请检查提示词或重试"]
            }
    
    def _create_error_response(self, error_msg: str) -> Dict[str, Any]:
        """创建错误响应
        
        Args:
            error_msg: 错误消息
            
        Returns:
            Dict[str, Any]: 错误响应格式
        """
        return {
            "is_compliant": False,
            "compliance_score": 0,
            "explanation": f"API调用失败: {error_msg}",
            "issues": ["API调用出错"],
            "suggestions": ["请检查配置和网络连接"]
        }
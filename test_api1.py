import requests

url = "https://api.siliconflow.cn/v1/chat/completions"

payload = {
    "model": "Qwen/QwQ-32B",
    "messages": [
        {
            "role": "user",
            "content": "JAVA单测测试案例质量评测"
        }
    ]
}
headers = {
    "Authorization": "Bearer sk-rsqzoocftapxiliruplirqwqnofttrnquovqerrydtgiyjha",
    "Content-Type": "application/json"
}

response = requests.post(url, json=payload, headers=headers)

print(response.json())
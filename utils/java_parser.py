import os
import re
import chardet
from pygments import lex
from pygments.lexers.jvm import JavaLexer
from pygments.token import Token

class JavaParser:
    """Java代码解析器，用于解析Java源代码和测试代码"""
    
    def __init__(self):
        """初始化解析器"""
        self.test_annotations = ['@Test', '@Before', '@After', '@BeforeClass', '@AfterClass']
        self.assertion_methods = [
            'assertEquals', 'assertTrue', 'assertFalse', 'assertNull', 'assertNotNull', 
            'assertSame', 'assertNotSame', 'assertArrayEquals', 'assertThrows'
        ]
    
    def read_file(self, file_path):
        """读取文件内容，自动检测编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件内容
        """
        try:
            # 首先尝试以二进制模式读取文件以检测编码
            with open(file_path, 'rb') as f:
                raw_data = f.read(4096)  # 读取前4KB进行编码检测
                encoding = chardet.detect(raw_data)['encoding'] or 'utf-8'
            
            # 然后使用检测到的编码打开文件
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {e}")
            return ""
    
    def is_test_file(self, file_path):
        """判断一个文件是否为测试文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为测试文件
        """
        # 基于文件路径判断
        if 'test' in file_path.lower():
            # 进一步检查文件内容
            content = self.read_file(file_path)
            for annotation in self.test_annotations:
                if annotation in content:
                    return True
        
        return False
    
    def extract_class_info(self, code):
        """提取Java类信息
        
        Args:
            code: Java代码内容
            
        Returns:
            dict: 包含类名、包名、是否为测试类等信息
        """
        package_pattern = r'package\s+([\w\.]+);'
        class_pattern = r'(?:public\s+|protected\s+|private\s+)?(?:abstract\s+)?class\s+(\w+)'
        
        package_match = re.search(package_pattern, code)
        class_match = re.search(class_pattern, code)
        
        class_info = {
            'package_name': package_match.group(1) if package_match else None,
            'class_name': class_match.group(1) if class_match else None,
            'is_test_class': False,
        }
        
        # 判断是否为测试类
        if class_info['class_name'] and (
            class_info['class_name'].endswith('Test') or 
            class_info['class_name'].startswith('Test') or
            '@TestClass' in code or 
            any(annotation in code for annotation in self.test_annotations)
        ):
            class_info['is_test_class'] = True
        
        return class_info
    
    def extract_test_methods(self, code):
        """提取Java测试方法
        
        Args:
            code: Java测试代码内容
            
        Returns:
            list: 测试方法列表，每个元素是包含方法名、代码内容等信息的字典
        """
        # 使用正则表达式匹配测试方法
        test_method_pattern = r'@Test.*?\s+(?:public\s+|protected\s+|private\s+)?(?:static\s+)?void\s+(\w+)\s*\([^)]*\)\s*(?:throws\s+[\w\s,]+)?\s*{([\s\S]*?)(?=\s*(?:@Test|\s*(?:public|protected|private)\s+(?:static\s+)?(?:void|[\w<>[\],\s]+)\s+\w+\s*\()|\s*}$)'
        
        methods = []
        for match in re.finditer(test_method_pattern, code, re.MULTILINE):
            method_name = match.group(1)
            method_body = match.group(2)
            method_start_index = match.start()
            method_start_line = code[:method_start_index].count('\n') + 1
            
            # 找到方法体的结束括号
            brackets = 1
            for i, char in enumerate(method_body):
                if char == '{':
                    brackets += 1
                elif char == '}':
                    brackets -= 1
                    if brackets == 0:
                        method_body = method_body[:i]
                        break
            
            methods.append({
                'name': method_name,
                'body': method_body.strip(),
                'assertions': self._extract_assertions(method_body),
                'line_count': len(method_body.strip().split('\n')),
                'start_line': method_start_line  # 添加起始行号
            })
        
        return methods
    
    def extract_source_methods(self, code):
        """提取Java源代码方法
        
        Args:
            code: Java源代码内容
            
        Returns:
            list: 方法列表，每个元素是包含方法名、代码内容等信息的字典
        """
        # 使用正则表达式匹配源代码方法
        method_pattern = r'(?:public|protected|private)\s+(?:static\s+)?(?:[\w<>[\],\s]+)\s+(\w+)\s*\([^)]*\)\s*(?:throws\s+[\w\s,]+)?\s*{([\s\S]*?)(?=\s*(?:(?:public|protected|private)\s+(?:static\s+)?(?:[\w<>[\],\s]+)\s+\w+\s*\()|\s*}$)'
        
        methods = []
        for match in re.finditer(method_pattern, code, re.MULTILINE):
            method_name = match.group(1)
            method_body = match.group(2)
            
            # 找到方法体的结束括号
            brackets = 1
            for i, char in enumerate(method_body):
                if char == '{':
                    brackets += 1
                elif char == '}':
                    brackets -= 1
                    if brackets == 0:
                        method_body = method_body[:i]
                        break
            
            methods.append({
                'name': method_name,
                'body': method_body.strip(),
                'line_count': len(method_body.strip().split('\n')),
                'complexity': self._estimate_complexity(method_body)
            })
        
        return methods
    
    def _extract_assertions(self, method_body):
        """提取测试方法中的断言
        
        Args:
            method_body: 方法体代码
            
        Returns:
            list: 断言列表
        """
        assertions = []
        for assertion in self.assertion_methods:
            # 查找所有断言及其位置
            for match in re.finditer(r'{}(\s*\([^;]+;)'.format(assertion), method_body):
                assertion_text = match.group(0)
                # 计算行号（基于方法体开始的相对行号）
                line_number = method_body[:match.start()].count('\n') + 1
                assertions.append({
                    'type': assertion,
                    'text': assertion_text,
                    'line': line_number
                })
        
        return assertions
    
    def _estimate_complexity(self, method_body):
        """估计方法的复杂度
        
        Args:
            method_body: 方法体代码
            
        Returns:
            int: 估计的复杂度
        """
        # 简单的复杂度估计：条件语句数量 + 循环数量 + 1
        complexity = 1
        complexity += len(re.findall(r'\bif\s*\(', method_body))
        complexity += len(re.findall(r'\belse\b', method_body))
        complexity += len(re.findall(r'\bfor\s*\(', method_body))
        complexity += len(re.findall(r'\bwhile\s*\(', method_body))
        complexity += len(re.findall(r'\bswitch\s*\(', method_body))
        complexity += len(re.findall(r'\bcatch\s*\(', method_body))
        
        return complexity
    
    def analyze_directory_structure(self, test_file_path, source_dir=None):
        """分析目录结构
        
        Args:
            test_file_path: 测试文件路径
            source_dir: 源代码目录（可选）
            
        Returns:
            dict: 目录结构分析结果
        """
        test_path = os.path.normpath(test_file_path)
        
        # 分析测试文件路径
        is_standard_structure = False
        test_in_test_dir = 'test' in test_path.lower()
        
        # 尝试从测试文件路径推断出对应的源代码文件路径
        source_file_path = None
        if source_dir and test_in_test_dir:
            # 尝试匹配标准Maven/Gradle结构
            if '/src/test/java/' in test_path:
                source_path = test_path.replace('/src/test/java/', '/src/main/java/')
                source_file_path = source_path.replace('Test.java', '.java')
                is_standard_structure = True
            # 尝试匹配简单的test目录结构
            elif '/test/' in test_path:
                source_path = test_path.replace('/test/', '/src/')
                source_file_path = source_path.replace('Test.java', '.java')
                is_standard_structure = '/src/' in source_path
        
        result = {
            'test_file_path': test_file_path,
            'is_standard_structure': is_standard_structure,
            'test_in_test_dir': test_in_test_dir,
            'inferred_source_path': source_file_path if source_file_path and os.path.exists(source_file_path) else None,
            'directory_compliance_score': 8 if is_standard_structure else (5 if test_in_test_dir else 2)
        }
        
        return result
    
    def analyze_token_stream(self, code):
        """分析代码的标记流
        
        Args:
            code: Java代码内容
            
        Returns:
            dict: 带有Token分析结果的字典
        """
        results = {
            'token_count': 0,
            'imports': [],
            'annotations': [],
            'class_definition': None,
            'method_calls': []
        }
        
        tokens = list(lex(code, JavaLexer()))
        results['token_count'] = len(tokens)
        
        # 分析导入语句
        import_statement = False
        current_import = ""
        
        # 分析注解和方法调用
        for i, (token_type, token_value) in enumerate(tokens):
            # 处理导入语句
            if token_type is Token.Keyword and token_value == 'import':
                import_statement = True
                current_import = ""
            elif import_statement:
                if token_value == ';':
                    import_statement = False
                    results['imports'].append(current_import.strip())
                else:
                    current_import += token_value
            
            # 处理注解
            if token_type is Token.Name.Decorator:
                results['annotations'].append(token_value)
            
            # 处理类定义
            if token_type is Token.Keyword and token_value == 'class' and i+2 < len(tokens):
                class_token = tokens[i+2]
                if class_token[0] is Token.Name.Class:
                    results['class_definition'] = class_token[1]
            
            # 处理方法调用
            if token_type is Token.Name.Function:
                if i > 0 and tokens[i-1][1] == '.':  # 确认是方法调用
                    results['method_calls'].append(token_value)
        
        return results
    
    def find_matching_source_file(self, test_file_path, source_dirs):
        """寻找与测试文件匹配的源代码文件
        
        Args:
            test_file_path: 测试文件路径
            source_dirs: 源代码目录列表
            
        Returns:
            str: 匹配的源代码文件路径，如果没有找到则返回None
        """
        # 从测试文件名推断源文件名
        filename = os.path.basename(test_file_path)
        if filename.endswith('Test.java'):
            source_filename = filename[:-9] + '.java'  # 移除'Test.java'并添加'.java'
        else:
            return None
        
        # 读取测试文件内容以提取包名
        test_code = self.read_file(test_file_path)
        test_class_info = self.extract_class_info(test_code)
        
        for source_dir in source_dirs:
            if not os.path.isdir(source_dir):
                continue
                
            # 遍历源代码目录寻找匹配文件
            for root, _, files in os.walk(source_dir):
                if source_filename in files:
                    source_path = os.path.join(root, source_filename)
                    source_code = self.read_file(source_path)
                    source_class_info = self.extract_class_info(source_code)
                    
                    # 如果包名匹配或者包名信息不可用，视为找到匹配
                    if (test_class_info['package_name'] is None or 
                        source_class_info['package_name'] is None or 
                        test_class_info['package_name'] == source_class_info['package_name']):
                        return source_path
        
        return None
    
    def analyze_naming_conventions(self, test_code, source_file_name=None):
        """分析测试代码的命名约定
        
        Args:
            test_code: 测试代码
            source_file_name: 源文件名（可选）
            
        Returns:
            dict: 命名规范分析结果
        """
        class_info = self.extract_class_info(test_code)
        test_methods = self.extract_test_methods(test_code)
        
        # 检查类名是否以Test结尾或以被测试类名+Test方式命名
        class_naming_correct = False
        source_class_name = None
        
        if source_file_name:
            source_class_name = source_file_name.replace('.java', '')
            class_naming_correct = class_info['class_name'] == source_class_name + 'Test'
        else:
            class_naming_correct = class_info['class_name'].endswith('Test')
        
        # 检查测试方法命名
        method_naming_scores = []
        method_naming_issues = []
        
        for method in test_methods:
            method_name = method['name']
            
            # 检查方法名是否以test开头
            starts_with_test = method_name.startswith('test') or method_name.startswith('should')
            
            # 检查方法名是否清晰描述了测试内容
            name_length = len(method_name)
            is_descriptive = name_length > 8  # 简单启发式：名称长度>8可能更具描述性
            
            # 检查命名风格一致性
            underscore_style = '_' in method_name
            camel_case = not underscore_style and sum(1 for c in method_name if c.isupper()) > 1
            
            # 计算方法命名得分（10分满分）
            method_score = 0
            if starts_with_test:
                method_score += 3
            if is_descriptive:
                method_score += 3
            if underscore_style or camel_case:
                method_score += 4
            
            method_naming_scores.append(method_score)
            
            if method_score < 7:  # 如果命名不太符合规范
                issues = []
                if not starts_with_test:
                    issues.append("方法名应以'test'或'should'开头")
                if not is_descriptive:
                    issues.append("方法名应更具描述性")
                if not (underscore_style or camel_case):
                    issues.append("方法名应遵循一致的命名风格（下划线或驼峰式）")
                
                method_naming_issues.append({
                    'method_name': method_name,
                    'issues': issues,
                    'start_line': method['start_line']  # 添加起始行号
                })
        
        # 计算整体命名规范得分
        class_score = 10 if class_naming_correct else 0
        method_avg_score = sum(method_naming_scores) / len(method_naming_scores) if method_naming_scores else 0
        overall_score = (class_score * 0.4) + (method_avg_score * 0.6)  # 类名权重40%，方法名权重60%
        
        return {
            'class_naming_correct': class_naming_correct,
            'class_name': class_info['class_name'],
            'source_class_name': source_class_name,
            'method_naming_issues': method_naming_issues,
            'overall_naming_score': overall_score
        }

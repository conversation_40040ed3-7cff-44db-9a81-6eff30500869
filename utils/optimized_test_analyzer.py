import os
import json
from .java_parser import JavaParser
from services.model_factory import model_factory
from config.config import Config

class OptimizedTestAnalyzer:
    """优化版测试分析器，精简分析流程，提升效率"""
    
    def __init__(self, api_service=None):
        """初始化优化版测试分析器
        
        Args:
            api_service: 模型服务实例（可选）
        """
        self.java_parser = JavaParser()
        
        # 初始化模型服务
        self.api_service = api_service or model_factory.get_model_service()
            
        self.config = Config()
        
        # 定义核心分析规则（移除非核心规则以提升效率）
        # 这里我们将LLM返回的规则名视为"设计原则"
        self.core_rules = {
            'independence': {'weight': 20, 'name': '独立性原则'},
            'error_handling': {'weight': 20, 'name': '错误处理'},
            'assertion_principle': {'weight': 20, 'name': '断言原则'},
            'boundary_values': {'weight': 15, 'name': '边界值测试'},
            'valid_inputs': {'weight': 10, 'name': '正确输入测试'},
            'core_code_principle': {'weight': 15, 'name': '核心代码原则'}
        }
        
    def analyze_test_file_optimized(self, test_file_path, source_file_path=None, progress_callback=None):
        """优化版单文件分析，精简处理流程
        
        Args:
            test_file_path: 测试文件路径
            source_file_path: 对应的源代码文件路径（可选）
            progress_callback: 进度回调函数（可选）
            
        Returns:
            dict: 简化的测试分析结果
        """
        try:
            if progress_callback:
                progress_callback("读取测试文件...", 10)
                
            # 读取测试文件内容
            test_code = self.java_parser.read_file(test_file_path)
            if not test_code:
                return {
                    'error': f"无法读取测试文件: {test_file_path}",
                    'score': 0
                }
                
            # 读取源代码文件（如果提供）
            source_code = None
            if source_file_path and os.path.exists(source_file_path):
                source_code = self.java_parser.read_file(source_file_path)
            
            if progress_callback:
                progress_callback("解析代码结构...", 20)
                
            # 提取基本信息
            class_info = self.java_parser.extract_class_info(test_code)
            test_methods = self.java_parser.extract_test_methods(test_code)
            
            if not class_info['is_test_class'] or not test_methods:
                return {
                    'error': "未找到有效的测试类或测试方法",
                    'score': 0,
                    'file_path': test_file_path
                }
            
            # 使用批量分析替代逐个分析，提升效率
            if progress_callback:
                progress_callback("执行智谱AI智能分析...", 40)
                
            print(f"开始智谱AI分析，测试文件: {test_file_path}")
            batch_analysis_result = self._batch_analyze_test_quality(test_code, source_code)
            print(f"智谱AI分析完成，结果类型: {type(batch_analysis_result)}")
            
            if progress_callback:
                progress_callback("计算质量评分...", 80)
                
            # 计算总得分，并准备详细/聚合的违规信息
            total_score = 0
            
            # 存储该文件所有详细的行级违规信息 (用于文件详情页)
            detailed_violations_for_file = [] 
            # 存储按设计原则聚合的违规数量和平均得分 (用于分析总览页)
            principle_violation_summary = {}

            for rule_name, rule_config in self.core_rules.items():
                principle_name = rule_config['name'] # 获取设计原则的名称
                rule_result = batch_analysis_result.get(rule_name, {})
                score = rule_result.get('compliance_score', 0)
                weight = rule_config['weight']
                
                # 计算加权得分
                total_score += (score / 10) * weight
                
                # 初始化原则汇总数据
                if principle_name not in principle_violation_summary:
                    principle_violation_summary[principle_name] = {
                        'violation_count': 0,
                        'total_principle_score': 0, # Sum of scores for rules under this principle
                        'rules_evaluated': 0, # Count of rules evaluated under this principle
                        'common_issues': [] # Brief issues for this principle
                    }

                # 收集详细的行级问题
                issues_from_llm = rule_result.get('issues', [])
                for issue in issues_from_llm:
                    detailed_violations_for_file.append({
                        'line_number': issue.get('line_number', 0), # Default to 0 if not provided
                        'principle_name': principle_name,
                        'rule_name': issue.get('rule_name', rule_name), # Specific rule from LLM, or principle name
                        'description': issue.get('description', '无描述'),
                        'suggestions': rule_result.get('suggestions', []) # Suggestions for this specific rule
                    })
                    principle_violation_summary[principle_name]['violation_count'] += 1 # Increment total violations for this principle
                
                # 更新原则的汇总分数和评估规则数量
                principle_violation_summary[principle_name]['total_principle_score'] += score
                principle_violation_summary[principle_name]['rules_evaluated'] += 1
                principle_violation_summary[principle_name]['common_issues'].extend(
                    issue.get('description', '') for issue in issues_from_llm[:1] # Take first issue description for principle summary
                )

            # 计算最终的原则平均得分
            final_principle_summary = {}
            for principle_name, data in principle_violation_summary.items():
                avg_score = data['total_principle_score'] / data['rules_evaluated'] if data['rules_evaluated'] > 0 else 0
                final_principle_summary[principle_name] = {
                    'violation_count': data['violation_count'],
                    'average_score': round(avg_score, 1),
                    'common_issues': list(set(data['common_issues'])) # Ensure unique common issues
                }
            
            if progress_callback:
                progress_callback("生成改进建议...", 90)
                
            # 生成简化的改进建议 (基于最终的原则汇总)
            improvement_suggestions = self._generate_quick_suggestions(final_principle_summary, total_score)
            
            if progress_callback:
                progress_callback("分析完成", 100)
            
            # 构建精简的分析结果
            final_result = {
                'file_path': test_file_path,
                'file_name': os.path.basename(test_file_path), # Add file_name for easy access in templates
                'class_name': class_info['class_name'],
                'method_count': len(test_methods),
                'score': round(total_score, 1),  # 精确到1位小数
                'grade': self._get_score_grade(total_score),
                'detailed_violations': detailed_violations_for_file, # 存储详细行级违规信息
                'principle_violation_summary': final_principle_summary, # 存储按原则汇总的违规信息
                'suggestions': improvement_suggestions,
                'analysis_time': self._get_analysis_timestamp()
            }
            
            return final_result
            
        except Exception as e:
            return {
                'error': f"分析测试文件时出错: {str(e)}",
                'file_path': test_file_path,
                'score': 0
            }
    
    def _batch_analyze_test_quality(self, test_code, source_code=None):
        """批量分析测试质量，减少API调用次数"""
        
        try:
            # 构建综合分析提示词
            context = f"""
            源代码：
            ```java
            {source_code}
            ```
            """ if source_code else ""
            
            prompt = f"""
            请对以下Java单元测试进行全面质量分析，基于6个核心原则进行评估：
            
            {context}
            
            测试代码：
            ```java
            {test_code}
            ```
            
            请分析以下6个核心原则：
            1. independence - 独立性原则：测试是否独立，不依赖其他测试
            2. error_handling - 错误处理：是否测试异常情况和错误输入
            3. assertion_principle - 断言原则：断言是否合理且有意义
            4. boundary_values - 边界值测试：是否测试边界条件
            5. valid_inputs - 正确输入测试：是否测试正常有效输入
            6. core_code_principle - 核心代码原则：是否测试核心业务逻辑
            
            请以JSON格式回答，每个原则包含：
            - compliance_score: 0-10的评分
            - issues: 数组，包含具体问题列表，每个问题应包含line_number（行号，如果适用，否则为0或null），description（问题描述），和rule_name（违反的子规则名称）。
            - suggestions: 数组，最多2个改进建议。
            
            JSON格式：
            {{
                "independence": {{"compliance_score": {{'X'}}, "issues": [{{"line_number": {{'Y'}}, "description": "...", "rule_name": "..."}}], "suggestions": [...]}},
                "error_handling": {{"compliance_score": {{'X'}}, "issues": [{{"line_number": {{'Y'}}, "description": "...", "rule_name": "..."}}], "suggestions": [...]}},
                "assertion_principle": {{"compliance_score": {{'X'}}, "issues": [{{"line_number": {{'Y'}}, "description": "...", "rule_name": "..."}}], "suggestions": [...]}},
                "boundary_values": {{"compliance_score": {{'X'}}, "issues": [{{"line_number": {{'Y'}}, "description": "...", "rule_name": "..."}}], "suggestions": [...]}},
                "valid_inputs": {{"compliance_score": {{'X'}}, "issues": [{{"line_number": {{'Y'}}, "description": "...", "rule_name": "..."}}], "suggestions": [...]}},
                "core_code_principle": {{"compliance_score": {{'X'}}, "issues": [{{"line_number": {{'Y'}}, "description": "...", "rule_name": "..."}}], "suggestions": [...]}}
            }}
            """
            
            # 调用智谱AI进行分析
            print(f"🤖 正在调用智谱AI GLM-4模型分析测试代码...")
            result = self.api_service._call_llm(prompt)
            print(f"🤖 智谱AI分析完成，返回结果: {type(result)}")
            
            # 验证返回结果格式
            if isinstance(result, dict) and all(rule in result for rule in self.core_rules.keys()):
                print("✅ 智谱AI分析成功，使用真实AI分析结果")
                return result
            else:
                # 如果格式不正确，记录错误但仍尝试使用返回的数据
                print(f"⚠️ 智谱AI返回格式异常，尝试修复: {result}")
                # 尝试修复格式或使用部分数据
                fixed_result = {}
                for rule_name in self.core_rules.keys():
                    if isinstance(result, dict) and rule_name in result:
                        fixed_result[rule_name] = result[rule_name]
                    else:
                        fixed_result[rule_name] = {
                            'compliance_score': 5,
                            'issues': [f"无法获取 {rule_name} 分析结果"],
                            'suggestions': ["请重新分析"]
                        }
                return fixed_result
            
        except Exception as e:
            # API调用失败，使用基于规则的分析作为后备
            print(f"❌ 智谱AI API调用异常: {str(e)}")
            # Fallback to rule-based analysis (this was removed previously, keeping as comment for context)
            # return self._rule_based_analysis(test_code, source_code)
            raise # Re-raise the exception to indicate failure to the caller


    # This method is no longer used as the fallback has been removed.
    # def _rule_based_analysis(self, test_code, source_code=None):
    #     # ... (original _rule_based_analysis content) ...
    #     pass
    
    def _generate_quick_suggestions(self, principle_violation_summary, total_score):
        """生成快速改进建议"""
        suggestions = {
            'priority': 'high' if total_score < 60 else 'medium' if total_score < 80 else 'low',
            'key_issues': [],
            'quick_fixes': [],
            'overall_advice': ""
        }
        
        # Sort principles by average score (lower score means more severe)
        # Using sorted_violations for principles
        sorted_principles = sorted(principle_violation_summary.items(), key=lambda x: x[1].get('average_score', 0))
        
        # Extract key issues from principles
        for principle_name, violation_data in sorted_principles[:3]:  # Take top 3 most severe principles
            suggestions['key_issues'].append({
                'principle': principle_name,
                'score': violation_data.get('average_score', 0),
                'main_issue': violation_data['common_issues'][0] if violation_data.get('common_issues') else '需要改进'
            })
        
        # Generate quick fixes based on overall score
        if total_score < 60:
            suggestions['quick_fixes'] = [
                "优先修复断言和错误处理问题",
                "确保测试方法独立且自动化",
                "增加边界值和异常情况测试"
            ]
            suggestions['overall_advice'] = "测试质量需要显著改进，建议重构主要测试逻辑"
        elif total_score < 80:
            suggestions['quick_fixes'] = [
                "完善现有测试用例的断言",
                "增加边界条件测试覆盖",
                "优化测试方法的粒度"
            ]
            suggestions['overall_advice'] = "测试基础良好，需要进一步完善测试覆盖和质量"
        else:
            suggestions['quick_fixes'] = [
                "微调测试细节提升质量",
                "考虑增加性能测试用例",
                "优化测试可读性和维护性"
            ]
            suggestions['overall_advice'] = "测试质量良好，可以进行细节优化"
        
        return suggestions
    
    def _get_score_grade(self, score):
        """根据分数获取等级"""
        if score >= 90:
            return {"level": "A+", "description": "优秀"}
        elif score >= 80:
            return {"level": "A", "description": "良好"}
        elif score >= 70:
            return {"level": "B", "description": "中等"}
        elif score >= 60:
            return {"level": "C", "description": "及格"}
        else:
            return {"level": "D", "description": "不及格"}
    
    def _get_analysis_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def analyze_project_optimized(self, project_dir, progress_callback=None):
        """批量分析整个项目，多个测试文件合并分析"""
        # Find all Java files
        java_files = []
        for root, _, files in os.walk(project_dir):
            for file in files:
                if file.endswith('.java'):
                    java_files.append(os.path.join(root, file))
        if not java_files:
            return {'error': f"在项目目录 {project_dir} 中未找到Java文件"}
        test_files = []
        source_files = []
        for file_path in java_files:
            if self.java_parser.is_test_file(file_path):
                test_files.append(file_path)
            else:
                source_files.append(file_path)
        if not test_files:
            return {'error': "未找到测试文件"}
        # 组装批量分析内容
        batch_items = []
        for test_file in test_files:
            test_code = self.java_parser.read_file(test_file)
            source_file = self.java_parser.find_matching_source_file(test_file, [os.path.dirname(f) for f in source_files])
            source_code = self.java_parser.read_file(source_file) if source_file else None
            batch_items.append({
                'file_path': test_file,
                'file_name': os.path.basename(test_file),
                'test_code': test_code,
                'source_code': source_code
            })
        # 构建批量Prompt
        prompt = "请对以下多个Java单元测试文件进行质量分析，基于6个核心原则分别评估。每个文件请单独输出JSON结果，结构如下：\n[\n  {\n    \"file_name\": \"xxx.java\",\n    \"principle_scores\": { ... }\n  }, ...\n]\n\n核心原则：\n1. independence - 独立性原则\n2. error_handling - 错误处理\n3. assertion_principle - 断言原则\n4. boundary_values - 边界值测试\n5. valid_inputs - 正确输入测试\n6. core_code_principle - 核心代码原则\n";
        for item in batch_items:
            prompt += f"\n文件名: {item['file_name']}\n测试代码:\n```java\n{item['test_code']}\n```\n"
            if item['source_code']:
                prompt += f"如有需要，可参考源代码：\n```java\n{item['source_code']}\n```\n"
        prompt += "\n请严格按照上述JSON数组格式返回，每个文件一个对象。"
        # 调用大模型批量分析
        result = self.api_service._call_llm(prompt)
        # 解析结果
        all_file_results = []
        analyzed_file_count = 0
        project_score = 0
        project_principle_summary = {}
        if isinstance(result, list):
            for file_result in result:
                all_file_results.append(file_result)
                if 'score' in file_result:
                    project_score += file_result['score']
                    analyzed_file_count += 1
                # 聚合原则统计
                file_principle_summary = file_result.get('principle_violation_summary', {})
                for principle_name, data in file_principle_summary.items():
                    if principle_name not in project_principle_summary:
                        project_principle_summary[principle_name] = {
                            'violation_count': 0,
                            'total_principle_score': 0,
                            'rules_evaluated': 0,
                            'common_issues': []
                        }
                    project_principle_summary[principle_name]['violation_count'] += data.get('violation_count', 0)
                    project_principle_summary[principle_name]['total_principle_score'] += data.get('average_score', 0) * data.get('rules_evaluated', 1)
                    project_principle_summary[principle_name]['rules_evaluated'] += data.get('rules_evaluated', 1)
                    project_principle_summary[principle_name]['common_issues'].extend(data.get('common_issues', []))
        else:
            # 兼容大模型返回单个文件或格式异常
            all_file_results.append(result)
            if 'score' in result:
                project_score += result['score']
                analyzed_file_count += 1
        # 计算平均分
        final_project_principle_summary = {}
        for principle_name, data in project_principle_summary.items():
            avg_score = data['total_principle_score'] / data['rules_evaluated'] if data['rules_evaluated'] > 0 else 0
            final_project_principle_summary[principle_name] = {
                'violation_count': data['violation_count'],
                'average_score': round(avg_score, 1),
                'common_issues': list(set(data['common_issues']))
            }
        final_project_result = {
            'project_score': round(project_score / analyzed_file_count, 1) if analyzed_file_count > 0 else 0,
            'analyzed_file_count': analyzed_file_count,
            'file_names': [os.path.basename(f) for f in test_files],
            'file_results': all_file_results,
            'rule_issues_summary': final_project_principle_summary,
            'analysis_summary': self._summarize_project_results(project_dir, test_files, source_files, all_file_results),
            'project_grade': self._get_score_grade(project_score / analyzed_file_count if analyzed_file_count > 0 else 0)
        }
        return final_project_result

    def _summarize_project_results(self, project_dir, test_files, source_files, analysis_results):
        # This function might need adjustment based on the new structure of analysis_results
        # For now, keep it as is, or simplify it if it's too complex given the new data.
        # It's currently used for 'analysis_summary' in the project result.

        total_test_files = len(test_files)
        total_source_files = len(source_files)
        total_methods = sum(f.get('method_count', 0) for f in analysis_results if not f.get('error'))
        
        overall_score = sum(f.get('score', 0) for f in analysis_results if not f.get('error')) / max(1, len(analysis_results))
        
        # Aggregate violations by principle for a quick summary
        principle_counts = {}
        for file_res in analysis_results:
            for principle, data in file_res.get('principle_violation_summary', {}).items():
                if principle not in principle_counts:
                    principle_counts[principle] = 0
                principle_counts[principle] += data.get('violation_count', 0)

        summary_text = f"本次分析共评估了 {total_test_files} 个测试文件，涉及 {total_methods} 个测试方法。项目整体质量评分为 {round(overall_score, 1)} 分。"
        
        if principle_counts:
            summary_text += " 主要质量问题分布在："
            issues_list = []
            for principle, count in principle_counts.items():
                if count > 0:
                    issues_list.append(f"{principle} ({count}个)")
            if issues_list:
                summary_text += ", ".join(issues_list) + "。"
            else:
                summary_text += " 没有发现明显的质量问题。"
        else:
            summary_text += " 未能获取详细的质量问题分布。"

        return {
            'overall_assessment': summary_text,
            'file_count': total_test_files,
            'source_file_count': total_source_files,
            'test_method_count': total_methods,
            'principles_summary': principle_counts # Add principle counts for potential display
        }
import os
import json
from .java_parser import JavaParser
from services.model_factory import model_factory
from config.config import Config

class TestAnalyzer:
    """测试分析器，用于分析Java测试的质量"""
    
    def __init__(self, api_service=None):
        """初始化测试分析器
        
        Args:
            api_service: 模型服务实例（可选）
        """
        self.java_parser = JavaParser()
        self.api_service = api_service or model_factory.get_model_service()
        self.config = Config()
        
    def analyze_test_file(self, test_file_path, source_file_path=None):
        """分析单个测试文件
        
        Args:
            test_file_path: 测试文件路径
            source_file_path: 对应的源代码文件路径（可选）
            
        Returns:
            dict: 测试分析结果
        """
        try:
            # 读取测试文件内容
            test_code = self.java_parser.read_file(test_file_path)
            if not test_code:
                return {
                    'error': f"无法读取测试文件: {test_file_path}",
                    'score': 0
                }
                
            # 尝试读取源代码文件（如果提供了路径）
            source_code = None
            if source_file_path and os.path.exists(source_file_path):
                source_code = self.java_parser.read_file(source_file_path)
            
            # 提取测试类信息和测试方法
            class_info = self.java_parser.extract_class_info(test_code)
            test_methods = self.java_parser.extract_test_methods(test_code)
            
            if not class_info['is_test_class'] or not test_methods:
                return {
                    'error': "未找到有效的测试类或测试方法",
                    'score': 0,
                    'file_path': test_file_path
                }
            
            # 开始分析各项指标
            analysis_results = {}
            
            # 1. 分析独立性原则
            independence_result = self.api_service.analyze_test_independence(test_code, source_code)
            analysis_results['independence'] = independence_result
            
            # 2. 分析边界值测试
            boundary_values_result = self.api_service.analyze_boundary_values(test_code, source_code)
            analysis_results['boundary_values'] = boundary_values_result
            
            # 3. 分析正确输入测试
            valid_inputs_result = self.api_service.analyze_valid_inputs(test_code)
            analysis_results['valid_inputs'] = valid_inputs_result
            
            # 4. 分析错误处理
            error_handling_result = self.api_service.analyze_error_handling(test_code)
            analysis_results['error_handling'] = error_handling_result
            
            # 5. 分析断言原则
            assertion_result = self.api_service.analyze_assertion_principle(test_code)
            analysis_results['assertion_principle'] = assertion_result
            
            # 6. 分析核心代码原则
            core_code_result = self.api_service.analyze_core_code_principle(test_code, source_code)
            analysis_results['core_code_principle'] = core_code_result
            
            # 7. 分析目录结构规范
            source_dir = os.path.dirname(source_file_path) if source_file_path else None
            directory_structure_result = self.java_parser.analyze_directory_structure(test_file_path, source_dir)
            analysis_results['directory_structure'] = {
                'is_compliant': directory_structure_result['is_standard_structure'],
                'compliance_score': directory_structure_result['directory_compliance_score'],
                'explanation': f"目录结构分析: {'符合标准' if directory_structure_result['is_standard_structure'] else '不完全符合标准'}",
                'issues': [] if directory_structure_result['is_standard_structure'] else ["测试文件路径不符合标准的Maven/Gradle结构"],
                'suggestions': [] if directory_structure_result['is_standard_structure'] else ["建议将测试文件移动到标准的测试目录结构中"]
            }
            
            # 8. 分析命名规范
            source_file_name = os.path.basename(source_file_path) if source_file_path else None
            naming_result = self.java_parser.analyze_naming_conventions(test_code, source_file_name)
            
            formatted_naming_issues = []
            for issue in naming_result['method_naming_issues']:
                formatted_issues_list = [f"{i}" for i in issue['issues']]
                formatted_naming_issues.append({
                    'method_name': issue['method_name'],
                    'start_line': issue['start_line'],
                    'description': f"方法 {issue['method_name']} 命名问题: {', '.join(formatted_issues_list)}"
                })

            analysis_results['naming_convention'] = {
                'is_compliant': naming_result['overall_naming_score'] >= 7,
                'compliance_score': naming_result['overall_naming_score'],
                'explanation': f"命名规范分析: {'符合规范' if naming_result['overall_naming_score'] >= 7 else '不完全符合规范'}",
                'issues': formatted_naming_issues,
                'suggestions': ["确保测试类名为[被测类名]Test", "测试方法名应以test或should开头，并清晰描述测试意图"]
            }
            
            # 计算总得分
            rule_scores = {}
            rule_violations = {}
            total_score = 0
            
            for rule, result in analysis_results.items():
                weight = self.config.RULE_WEIGHTS.get(rule, 10)  # 默认权重为10
                score = result.get('compliance_score', 0)
                rule_scores[rule] = score
                
                issues = result.get('issues', [])
                suggestions = result.get('suggestions', [])
                rule_violations[rule] = {
                    'issues': issues,
                    'suggestions': suggestions,
                    'score': score
                }
                
                # 计算加权得分
                total_score += (score / 10) * weight  # 将每条规则的10分制转换为权重分
            
            # 获取改进建议
            improvement_suggestions = None
            try:
                improvement_suggestions = self.api_service.suggest_test_improvements(test_code, analysis_results)
            except Exception as e:
                improvement_suggestions = {
                    'error': f"获取改进建议时出错: {str(e)}"
                }
            
            # 构建最终分析结果
            final_result = {
                'file_path': test_file_path,
                'class_name': class_info['class_name'],
                'method_count': len(test_methods),
                'score': round(total_score, 2),  # 四舍五入到2位小数
                'rule_scores': rule_scores,
                'rule_violations': rule_violations,
                'improvement_suggestions': improvement_suggestions,
                'detailed_analysis': analysis_results
            }
            
            return final_result
            
        except Exception as e:
            return {
                'error': f"分析测试文件时出错: {str(e)}",
                'file_path': test_file_path,
                'score': 0
            }
    
    def analyze_project(self, project_dir, output_file=None):
        """分析整个项目中的所有测试
        
        Args:
            project_dir: 项目目录路径
            output_file: 输出结果的文件路径（可选）
            
        Returns:
            dict: 项目测试分析结果
        """
        # 查找所有Java文件
        java_files = []
        for root, _, files in os.walk(project_dir):
            for file in files:
                if file.endswith('.java'):
                    java_files.append(os.path.join(root, file))
        
        if not java_files:
            return {'error': f"在项目目录 {project_dir} 中未找到Java文件"}
            
        # 区分测试文件和源代码文件
        test_files = []
        source_files = []
        
        for file_path in java_files:
            if self.java_parser.is_test_file(file_path):
                test_files.append(file_path)
            else:
                source_files.append(file_path)
                
        if not test_files:
            return {'error': "未找到测试文件"}
            
        # 为每个测试文件找到对应的源代码文件（如果可能）
        test_source_pairs = []
        for test_file in test_files:
            source_file = self.java_parser.find_matching_source_file(test_file, [os.path.dirname(f) for f in source_files])
            test_source_pairs.append((test_file, source_file))
        
        # 分析每个测试文件
        analysis_results = []
        for test_file, source_file in test_source_pairs:
            result = self.analyze_test_file(test_file, source_file)
            analysis_results.append(result)
            
        # 计算项目总体得分
        total_score = 0
        file_count = 0
        for result in analysis_results:
            if 'score' in result and not result.get('error'):
                total_score += result['score']
                file_count += 1
                
        project_score = round(total_score / file_count, 2) if file_count > 0 else 0
        
        # 汇总规则违反情况
        rule_violations_summary = {}
        for rule in self.config.RULE_WEIGHTS:
            rule_violations_summary[rule] = {
                'violation_count': 0,
                'common_issues': set(),
                'average_score': 0
            }
            
        for result in analysis_results:
            if 'rule_violations' in result:
                for rule, violation in result['rule_violations'].items():
                    if rule in rule_violations_summary:
                        issues = violation.get('issues', [])
                        rule_violations_summary[rule]['violation_count'] += len(issues)
                        for issue in issues:
                            # 将字典或其他不可哈希类型转换为字符串，以避免unhashable type错误
                            if isinstance(issue, dict):
                                issue = json.dumps(issue, ensure_ascii=False)
                            elif not isinstance(issue, (str, int, float, bool, tuple)):
                                issue = str(issue)
                            rule_violations_summary[rule]['common_issues'].add(issue)
                        
                        score = violation.get('score', 0)
                        rule_violations_summary[rule]['average_score'] += score / len(analysis_results)
        
        # 将set转换为list以便JSON序列化
        for rule in rule_violations_summary:
            rule_violations_summary[rule]['common_issues'] = list(rule_violations_summary[rule]['common_issues'])
            rule_violations_summary[rule]['average_score'] = round(rule_violations_summary[rule]['average_score'], 2)
        
        # 构建最终项目分析结果
        project_result = {
            'project_dir': project_dir,
            'test_file_count': len(test_files),
            'source_file_count': len(source_files),
            'analyzed_file_count': file_count,
            'project_score': project_score,
            'rule_violations_summary': rule_violations_summary,
            'file_results': analysis_results
        }
        
        # 如果指定了输出文件，则将结果保存到文件
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(project_result, f, ensure_ascii=False, indent=2)
            except Exception as e:
                project_result['output_error'] = f"保存分析结果到文件时出错: {str(e)}"
        
        return project_result

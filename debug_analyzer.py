#!/usr/bin/env python3
"""调试测试分析器的问题"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.test_analyzer import TestAnalyzer
    from services.model_factory import model_factory
    
    print("=== 调试测试分析器 ===")
    
    # 创建一个简单的测试代码
    test_code = """
    import org.junit.Test;
    import static org.junit.Assert.*;
    
    public class CalculatorTest {
        
        @Test
        public void testAdd() {
            Calculator calc = new Calculator();
            assertEquals(5, calc.add(2, 3));
            assertEquals(0, calc.add(0, 0));
        }
        
        @Test
        public void testSubtract() {
            Calculator calc = new Calculator();
            assertEquals(1, calc.subtract(3, 2));
        }
    }
    """
    
    # 创建测试分析器
    analyzer = TestAnalyzer()
    print(f"分析器创建成功")
    print(f"API服务: {analyzer.api_service}")
    
    # 测试各个分析方法
    print("\n=== 测试各个分析方法 ===")
    
    try:
        print("1. 测试独立性分析...")
        independence_result = analyzer.api_service.analyze_test_independence(test_code, None)
        print(f"   结果类型: {type(independence_result)}")
        if isinstance(independence_result, dict):
            print(f"   包含字段: {list(independence_result.keys())}")
            if "error" in independence_result:
                print(f"   ❌ 错误: {independence_result['error']}")
            else:
                print(f"   ✅ 成功")
        else:
            print(f"   ❌ 返回类型错误: {independence_result}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        import traceback
        traceback.print_exc()
    
    try:
        print("\n2. 测试边界值分析...")
        boundary_result = analyzer.api_service.analyze_boundary_values(test_code, None)
        print(f"   结果类型: {type(boundary_result)}")
        if isinstance(boundary_result, dict):
            print(f"   包含字段: {list(boundary_result.keys())}")
            if "error" in boundary_result:
                print(f"   ❌ 错误: {boundary_result['error']}")
            else:
                print(f"   ✅ 成功")
        else:
            print(f"   ❌ 返回类型错误: {boundary_result}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        import traceback
        traceback.print_exc()
    
    try:
        print("\n3. 测试正确输入分析...")
        valid_inputs_result = analyzer.api_service.analyze_valid_inputs(test_code)
        print(f"   结果类型: {type(valid_inputs_result)}")
        if isinstance(valid_inputs_result, dict):
            print(f"   包含字段: {list(valid_inputs_result.keys())}")
            if "error" in valid_inputs_result:
                print(f"   ❌ 错误: {valid_inputs_result['error']}")
            else:
                print(f"   ✅ 成功")
        else:
            print(f"   ❌ 返回类型错误: {valid_inputs_result}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        import traceback
        traceback.print_exc()

except Exception as e:
    print(f"导入模块失败: {e}")
    import traceback
    traceback.print_exc()

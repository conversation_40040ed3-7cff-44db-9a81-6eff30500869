{% extends "layout/base.html" %}

{% block title %}模型设置{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-robot"></i> 模型设置
                    </h3>
                </div>
                <div class="card-body">
                    <!-- 当前模型状态 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-brain"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">当前使用模型</span>
                                    <span class="info-box-number" id="current-model-name">{{ current_model or 'openrouter' }}</span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 100%"></div>
                                    </div>
                                    <span class="progress-description" id="current-model-status">点击测试按钮检查连接状态</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">可用模型数量</span>
                                    <span class="info-box-number" id="available-models-count">{{ models|length if models else 0 }}</span>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" style="width: 100%"></div>
                                    </div>
                                    <span class="progress-description">模型服务状态</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模型列表 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">模型列表</h4>
                                    <button class="btn btn-sm btn-primary float-right" onclick="refreshModels()">
                                        <i class="fas fa-sync-alt"></i> 刷新
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover" id="models-table">
                                            <thead>
                                                <tr>
                                                    <th>模型名称</th>
                                                    <th>类型</th>
                                                    <th>提供商</th>
                                                    <th>状态</th>
                                                    <th>默认</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="models-tbody">
                                                {% if models %}
                                                    {% for model in models %}
                                                    <tr>
                                                        <td>{{ model.model_name or model.model_type }}</td>
                                                        <td><span>{{ model.model_type }}</span></td>
                                                        <td>{{ model.provider or 'Unknown' }}</td>
                                                        <td>
                                                            <span class="{{ 'text-success' if model.is_available else 'text-danger' }}" id="status-{{ model.model_type }}">
                                                                {{ '✓ 可用' if model.is_available else '✗ 不可用' }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span>
                                                                {{ '默认' if model.is_default else '' }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <button class="btn btn-sm btn-info config-btn" data-model-type="{{ model.model_type }}">
                                                                <i class="fas fa-cog"></i> 配置
                                                            </button>
                                                            <button class="btn btn-sm btn-warning test-btn" data-model-type="{{ model.model_type }}">
                                                                <i class="fas fa-vial"></i> 测试
                                                            </button>
                                                            {% if not model.is_default %}
                                                                <button class="btn btn-sm btn-primary" onclick="switchToModel('{{ model.model_type }}')">
                                                                    <i class="fas fa-exchange-alt"></i> 切换
                                                                </button>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="6" class="text-center">暂无可用模型</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模型配置模态框 -->
                    <div class="modal fade" id="configModal" tabindex="-1" role="dialog" aria-modal="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">模型配置</h4>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal">
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <form id="configForm">
                                        <div id="config-fields">
                                            <!-- 动态生成的配置字段 -->
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                    <button type="button" class="btn btn-primary" onclick="saveConfig()">保存配置</button>
                                    <button type="button" class="btn btn-info" id="test-connection-btn" onclick="testConnection()">测试连接</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentConfigModel = null;

$(document).ready(function() {
    // 页面加载时不进行API调用，仅初始化页面
    console.log('模型设置页面初始化完成');
    loadCurrentModelInfo(); // 确保页面加载时加载当前模型信息
    loadModels(); // 确保页面加载时加载模型列表

    // Event delegation for dynamically added buttons
    $('#models-tbody').on('click', '.config-btn', function() {
        const modelType = $(this).data('model-type');
        showConfig(modelType);
    });

    $('#models-tbody').on('click', '.test-btn', function() {
        const modelType = $(this).data('model-type');
        testModel(modelType);
    });
});

function loadCurrentModelInfo() {
    // 手动调用时才加载当前模型信息
    $.get('/api/models/current')
        .done(function(response) {
            if (response.success) {
                const modelInfo = response.model_info;
                $('#current-model-name').text(modelInfo.model_name || modelInfo.model_type);
                $('#current-model-status').text(modelInfo.is_available ? '✓ 可用' : '✗ 不可用');
                $('#current-model-status').removeClass('text-success text-danger')
                    .addClass(modelInfo.is_available ? 'text-success' : 'text-danger');
            }
        })
        .fail(function(xhr) {
            $('#current-model-name').text('加载失败');
            $('#current-model-status').text('状态未知');
        });
}

function loadModels() {
    // 手动调用时才加载模型列表
    $.get('/api/models')
        .done(function(response) {
            if (response.success) {
                renderModelsTable(response.models);
                updateAvailableCount(response.models);
            }
        })
        .fail(function(xhr) {
            showAlert('加载模型列表失败', 'error');
        });
}

function renderModelsTable(models) {
    const tbody = $('#models-tbody');
    tbody.empty();
    
    models.forEach(function(model) {
        const row = `
            <tr>
                <td>${model.model_name || model.model_type}</td>
                <td><span>${model.model_type}</span></td>
                <td>${model.provider || 'Unknown'}</td>
                <td>
                    <span class="${model.is_available ? 'text-success' : 'text-danger'}" data-model-type="${model.model_type}">
                        ${model.is_available ? '✓ 可用' : '✗ 不可用'}
                    </span>
                </td>
                <td>
                    <span>
                        ${model.is_default ? '默认' : ''}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-info config-btn" data-model-type="${model.model_type}">
                        <i class="fas fa-cog"></i> 配置
                    </button>
                    <button class="btn btn-sm btn-warning test-btn" data-model-type="${model.model_type}">
                        <i class="fas fa-vial"></i> 测试
                    </button>
                    ${!model.is_default ? `
                        <button class="btn btn-sm btn-primary" onclick="switchToModel('${model.model_type}')">
                            <i class="fas fa-exchange-alt"></i> 切换
                        </button>
                    ` : ''}
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function updateAvailableCount(models) {
    const availableCount = models.filter(m => m.is_available).length;
    $('#available-models-count').text(availableCount + '/' + models.length);
}

function showConfig(modelType) {
    currentConfigModel = modelType;
    
    // 获取模型配置
    $.get('/api/models/' + modelType + '/config')
        .done(function(response) {
            if (response.success) {
                renderConfigForm(response.config, modelType);
                // 使用 Bootstrap 5 的原生 JavaScript 方式显示模态框
                const configModal = new bootstrap.Modal(document.getElementById('configModal'));
                configModal.show();
            }
        })
        .fail(function(xhr) {
            showAlert('获取模型配置失败', 'error');
        });
}

function renderConfigForm(config, modelType) {
    const fieldsContainer = $('#config-fields');
    fieldsContainer.empty();
    
    // 不同模型类型的配置字段
    const fieldConfigs = {
        'openrouter': [
            { key: 'api_key', label: 'API密钥', type: 'password', required: true },
            { key: 'model', label: '模型名称', type: 'text', required: true, placeholder: '如qwen/qwen3-coder:free' },
            { key: 'site_url', label: '站点URL', type: 'text', required: false },
            { key: 'site_name', label: '站点名称', type: 'text', required: false },
            { key: 'description', label: '描述', type: 'textarea', required: false }
        ],
        'enterprise': [
            { key: 'url', label: '接口地址', type: 'url', required: true },
            { key: 'app_id', label: '应用ID', type: 'text', required: true },
            { key: 'username', label: '用户名', type: 'text', required: true },
            { key: 'api_secret', label: 'API密钥', type: 'password', required: true },
            { key: 'timeout', label: '超时时间(秒)', type: 'number', required: false, min: 1, max: 300 },
            { key: 'max_retries', label: '最大重试次数', type: 'number', required: false, min: 1, max: 10 }
        ]
    };
    
    const fields = fieldConfigs[modelType] || [];
    
    fields.forEach(function(field) {
        const value = config[field.key] || '';
        const fieldHtml = `
            <div class="form-group">
                <label for="${field.key}">${field.label}${field.required ? ' *' : ''}</label>
                ${field.type === 'textarea' ? 
                    `<textarea class="form-control" id="${field.key}" name="${field.key}" ${field.required ? 'required' : ''}>${value}</textarea>` :
                    `<input type="${field.type}" class="form-control" id="${field.key}" name="${field.key}" value="${value}" ${field.required ? 'required' : ''} ${field.min ? 'min="' + field.min + '"' : ''} ${field.max ? 'max="' + field.max + '"' : ''}>`
                }
            </div>
        `;
        fieldsContainer.append(fieldHtml);
    });
}

function saveConfig() {
    if (!currentConfigModel) return;
    
    const formData = new FormData(document.getElementById('configForm'));
    const config = {};
    
    for (let [key, value] of formData.entries()) {
        config[key] = value;
    }
    
    $.ajax({
        url: '/api/models/' + currentConfigModel + '/config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ config: config }),
        success: function(response) {
            if (response.success) {
                showAlert('配置保存成功', 'success');
                // 使用 Bootstrap 5 的原生 JavaScript 方式隐藏模态框
                const configModal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
                if (configModal) {
                    configModal.hide();
                }
                loadModels();
            } else {
                showAlert('配置保存失败: ' + response.error, 'error');
            }
        },
        error: function(xhr) {
            showAlert('配置保存失败', 'error');
        }
    });
}

function testConnection() {
    if (!currentConfigModel) return;
    
    const testBtn = $('#test-connection-btn');
    testBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 测试中...');
    
    $.ajax({
        url: '/api/models/' + currentConfigModel + '/test',
        method: 'POST',
        success: function(response) {
            const message = response.message || '测试完成';
            showAlert(message, response.success ? 'success' : 'error');
            if (response.success) {
                loadModels(); // 刷新模型列表以显示最新状态
            }
        },
        error: function(xhr) {
            showAlert('连接测试失败', 'error');
        },
        complete: function() {
            testBtn.prop('disabled', false).html('<i class="fas fa-vial"></i> 测试连接');
        }
    });
}

function testModel(modelType) {
    const testBtn = $('.test-btn[data-model-type="' + modelType + '"]');
    testBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 测试中...');

    $.ajax({
        url: '/api/models/' + modelType + '/test',
        method: 'POST',
        success: function(response) {
            const message = response.message || '测试完成';
            showAlert(message, response.success ? 'success' : 'error');
            testBtn.prop('disabled', false).html('<i class="fas fa-vial"></i> 测试'); // Restore button
            if (response.success) {
                loadModels(); // 刷新模型列表以显示最新状态
            }
        },
        error: function(xhr) {
            showAlert('连接测试失败', 'error');
            testBtn.prop('disabled', false).html('<i class="fas fa-vial"></i> 测试'); // Restore button
        }
    });
}

function switchToModel(modelType) {
    if (confirm('确定要切换到 ' + modelType + ' 模型吗？')) {
        $.ajax({
            url: '/api/models/switch',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ model_type: modelType }),
            success: function(response) {
                if (response.success) {
                    showAlert('模型切换成功', 'success');
                    loadCurrentModelInfo();
                    loadModels();
                } else {
                    showAlert('模型切换失败: ' + response.message, 'error');
                }
            },
            error: function(xhr) {
                showAlert('模型切换失败', 'error');
            }
        });
    }
}

function refreshModels() {
    // 手动刷新时重新加载页面
    window.location.reload();
}

function showAlert(message, type) {
    const alertType = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertType} alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            ${message}
        </div>
    `;
    
    $('.card-body').prepend(alertHtml);
    
    // 3秒后自动关闭
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
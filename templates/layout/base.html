<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Java单元测试质量分析器{% endblock %}</title>
    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
    <!-- Inter Font (Local) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fonts/inter.css') }}">
    <!-- Chart.js -->
    <script src="{{ url_for('static', filename='vendor/chartjs/chart.js') }}"></script>
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-vial me-2"></i>Java单元测试质量分析器
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    style="border: none; background: none;">
                <i class="fas fa-bars" style="color: var(--dark);"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('new_project') }}">
                            <i class="fas fa-plus-circle me-1"></i>新建项目
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('model_settings') }}">
                            <i class="fas fa-robot me-1"></i>模型设置
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <div class="container mt-4">
        <!-- 消息提醒 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 内容块 -->
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <footer class="footer mt-5 py-4">
        <div class="container text-center">
            <div class="mb-3">
                <i class="fas fa-vial fa-2x mb-2" style="background: var(--primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
            </div>
            <span class="text-muted">Java单元测试质量分析器 &copy; {{ now.year }} |
            基于先进的大语言模型技术</span>
            <div class="mt-2 small text-muted">
                让代码质量分析更智能，让测试更可靠
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
    {% block extra_js %}{% endblock %}
</body>
</html>

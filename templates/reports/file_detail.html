{% extends 'layout/base.html' %}

{% block title %}文件分析 - {{ test_file.file_name }}{% endblock %}

{% block extra_css %}
<style>
/* 代码片段高亮样式 */
.source.highlight {
    border-radius: 4px;
    overflow-x: auto;
}

.source.highlight .highlighttable {
    width: 100%;
    margin: 0;
}

.source.highlight .linenos {
    background: #f8f9fa;
    color: #6c757d;
    border-right: 1px solid #e9ecef;
    text-align: right;
    padding-right: 8px;
    user-select: none;
}

.source.highlight .code {
    padding-left: 12px;
}

/* 高亮问题行样式 */
.source.highlight .hll {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
    display: block;
    margin-left: -8px;
    padding-left: 4px;
}

.source.highlight .linenos .hll {
    background-color: #ffc107 !important;
    color: #212529 !important;
    font-weight: bold;
}

/* 代码语法高亮颜色 */
.source.highlight .k { color: #d73a49; font-weight: bold; }    /* 关键字 */
.source.highlight .s { color: #032f62; }                        /* 字符串 */
.source.highlight .mi { color: #005cc5; }                       /* 数字 */
.source.highlight .c1 { color: #6a737d; font-style: italic; }  /* 注释 */
.source.highlight .nc { color: #6f42c1; font-weight: bold; }    /* 类名 */
.source.highlight .nf { color: #6f42c1; }                       /* 函数名 */
.source.highlight .o { color: #d73a49; }                        /* 操作符 */

/* 代码片段容器样式 */
.code-snippet-container {
    margin-top: 8px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #f8f9fa;
}

.code-snippet-container .source.highlight {
    margin: 0;
    border-radius: 0 0 4px 4px;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('project_detail', project_id=project.id) }}">{{ project.name }}</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('analysis_detail', analysis_id=analysis.id) }}">分析报告 #{{ analysis.id }}</a></li>
                <li class="breadcrumb-item active">{{ test_file.file_name }}</li>
            </ol>
        </nav>
    </div>
</div>

<!-- 分析结果详情 -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-microscope"></i> 违规详情与源码定位</h5>
            </div>
            <div class="card-body">
                {% if detailed_violations %}
                    <!-- 过滤有效违规（行号大于0） -->
                    {% set valid_violations = detailed_violations | selectattr("line_number") | selectattr("line_number", "greaterthan", 0) | list %}
                    
                    {% if valid_violations %}
                    <!-- 新的详细违规显示格式 -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>检测到的违规 ({{ valid_violations|length }} 个)</h6>
                            
                            {% for violation in valid_violations %}
                            <div class="card mb-3 border-start border-warning border-4">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-1 fw-bold text-warning">{{ violation.principle_name }}</h6>
                                        <span class="badge bg-info">
                                            <i class="fas fa-map-marker-alt me-1"></i>行 {{ violation.line_number }}
                                        </span>
                                    </div>
                                    
                                    <div class="small text-muted mb-2">
                                        <i class="fas fa-tag me-1"></i>规则: {{ violation.rule_name }}
                                    </div>
                                    
                                    <p class="mb-2">{{ violation.description }}</p>
                                    
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-outline-primary show-code-snippet" 
                                                data-line="{{ violation.line_number }}" 
                                                data-file-id="{{ test_file.id }}">
                                            <i class="fas fa-code me-1"></i>查看源码
                                        </button>
                                    </div>
                                    <div id="code-snippet-{{ violation.line_number }}" class="code-snippet-container mt-2" style="display: none;"></div>
                                    
                                    {% if violation.suggestions %}
                                    <div class="mt-2">
                                        <small class="text-muted">建议:</small>
                                        <ul class="small mb-0 ps-3">
                                            {% for suggestion in violation.suggestions[:2] %}
                                            <li class="text-info">{{ suggestion }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>未发现具体代码行的质量问题
                    </div>
                    {% endif %}
                    
                {% elif analysis_result.detailed_analysis %}
                    <!-- 原始详细分析格式（保持向后兼容） -->
                    <ul class="nav nav-tabs" id="ruleTab" role="tablist">
                        {% for rule, result in analysis_result.detailed_analysis.items() %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link {{ 'active' if loop.first else '' }}" 
                                    id="{{ rule }}-tab" 
                                    data-bs-toggle="tab" 
                                    data-bs-target="#{{ rule }}" 
                                    type="button" 
                                    role="tab" 
                                    aria-controls="{{ rule }}" 
                                    aria-selected="{{ 'true' if loop.first else 'false' }}">
                                {{ rule|replace('_', ' ')|title }}
                                <span class="badge bg-{{ 'success' if result.compliance_score >= 8 else ('warning' if result.compliance_score >= 5 else 'danger') }} ms-1">
                                    {{ result.compliance_score }}
                                </span>
                            </button>
                        </li>
                        {% endfor %}
                    </ul>
                    <div class="tab-content p-3 border border-top-0 rounded-bottom" id="ruleTabContent">
                        {% for rule, result in analysis_result.detailed_analysis.items() %}
                        <div class="tab-pane fade {{ 'show active' if loop.first else '' }}" 
                             id="{{ rule }}" 
                             role="tabpanel" 
                             aria-labelledby="{{ rule }}-tab">
                            <h5>{{ rule|replace('_', ' ')|title }}</h5>
                            <p>{{ result.explanation }}</p>
                            
                            {% if result.issues %}
                            <div class="alert alert-{{ 'warning' if result.compliance_score >= 5 else 'danger' }}">
                                <h6>存在的问题:</h6>
                                <ul>
                                    {% for issue in result.issues %}
                                        {% if issue.description is defined and issue.start_line is defined %}
                                        <li>方法 {{ issue.method_name }} (行: {{ issue.start_line }}): {{ issue.description }} <a href="#" class="show-code-snippet" data-line="{{ issue.start_line }}" data-file-id="{{ test_file.id }}"><i class="fas fa-code"></i> 查看代码</a></li>
                                        <div id="code-snippet-{{ issue.start_line }}" class="code-snippet-container mt-2" style="display: none;"></div>
                                        {% else %}
                                        <li>{{ issue }}</li>
                                        {% endif %}
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                            
                            {% if result.suggestions %}
                            <div class="alert alert-info">
                                <h6>改进建议:</h6>
                                <ul>
                                    {% for suggestion in result.suggestions %}
                                    <li>{{ suggestion }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    
                {% elif analysis_result.violations %}
                    <!-- 原始优化版分析格式（保持向后兼容） -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>检测到的问题</h6>
                            {% if analysis_result.violations %}
                                {% for rule_name, violation in analysis_result.violations.items() %}
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <strong>{{ violation.rule_name or rule_name|replace('_', ' ')|title }}</strong>
                                            <span class="badge bg-{{ 'success' if violation.score >= 8 else ('warning' if violation.score >= 5 else 'danger') }}">
                                                {{ violation.score }}/10
                                            </span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        {% if violation.issues %}
                                        <div class="mb-3">
                                            <small class="text-muted">问题:</small>
                                            <ul class="mb-0">
                                                {% for issue in violation.issues %}
                                                <li>{{ issue }}</li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                        {% endif %}
                                        
                                        {% if violation.suggestions %}
                                        <div>
                                            <small class="text-muted">建议:</small>
                                            <ul class="mb-0">
                                                {% for suggestion in violation.suggestions %}
                                                <li class="text-info">{{ suggestion }}</li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>未发现明显问题
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        暂无详细分析数据，请重新分析该文件。
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // 源代码查看功能
    document.addEventListener('DOMContentLoaded', function() {
        const showCodeButtons = document.querySelectorAll('.show-code-snippet');
        
        showCodeButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const lineNumber = this.getAttribute('data-line');
                const fileId = this.getAttribute('data-file-id');
                const snippetContainer = document.getElementById('code-snippet-' + lineNumber);
                
                if (snippetContainer.style.display === 'none') {
                    // 显示代码片段
                    snippetContainer.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-eye-slash me-1"></i>隐藏源码';
                    
                    // 如果还没有加载代码，则加载代码
                    if (!snippetContainer.innerHTML.trim()) {
                        fetch(`/api/files/${fileId}/code_snippet?line=${lineNumber}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.code) {
                                    let headerContent = `
                                        <i class="fas fa-file-code me-1"></i>
                                        显示第 ${data.start_line}-${data.end_line} 行，共 ${data.total_lines} 行
                                    `;
                                    
                                    if (data.note) {
                                        // 行号超出范围的情况
                                        headerContent += `
                                            <span class="float-end text-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>${data.note}
                                            </span>
                                        `;
                                    } else {
                                        // 正常情况，显示问题行
                                        headerContent += `
                                            <span class="float-end">
                                                <i class="fas fa-crosshairs me-1"></i>问题行：第 ${data.highlight_line} 行
                                            </span>
                                        `;
                                    }
                                    
                                    snippetContainer.innerHTML = `
                                        <div class="code-snippet-header bg-secondary text-white px-3 py-2 small">
                                            ${headerContent}
                                        </div>
                                        <div class="code-content">
                                            ${data.code}
                                        </div>
                                    `;
                                } else {
                                    snippetContainer.innerHTML = '<div class="alert alert-warning">无法加载代码片段</div>';
                                }
                            })
                            .catch(error => {
                                snippetContainer.innerHTML = '<div class="alert alert-danger">加载代码片段时出错</div>';
                            });
                    }
                } else {
                    // 隐藏代码片段
                    snippetContainer.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-code me-1"></i>查看源码';
                }
            });
        });
    });
</script>
{% endblock %}
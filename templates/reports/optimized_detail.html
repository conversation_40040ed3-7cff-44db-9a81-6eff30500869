{% extends 'layout/base.html' %}

{% block title %}分析报告 - {{ project.name }}{% endblock %}

{% block extra_css %}
<style>
/* 代码片段高亮样式 */
.source.highlight {
    border-radius: 4px;
    overflow-x: auto;
}

.source.highlight .highlighttable {
    width: 100%;
    margin: 0;
}

.source.highlight .linenos {
    background: #f8f9fa;
    color: #6c757d;
    border-right: 1px solid #e9ecef;
    text-align: right;
    padding-right: 8px;
    user-select: none;
}

.source.highlight .code {
    padding-left: 12px;
}

/* 高亮问题行样式 */
.source.highlight .hll {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
    display: block;
    margin-left: -8px;
    padding-left: 4px;
}

.source.highlight .linenos .hll {
    background-color: #ffc107 !important;
    color: #212529 !important;
    font-weight: bold;
}

/* 代码语法高亮颜色 */
.source.highlight .k { color: #d73a49; font-weight: bold; }    /* 关键字 */
.source.highlight .s { color: #032f62; }                        /* 字符串 */
.source.highlight .mi { color: #005cc5; }                       /* 数字 */
.source.highlight .c1 { color: #6a737d; font-style: italic; }  /* 注释 */
.source.highlight .nc { color: #6f42c1; font-weight: bold; }    /* 类名 */
.source.highlight .nf { color: #6f42c1; }                       /* 函数名 */
.source.highlight .o { color: #d73a49; }                        /* 操作符 */

/* 代码片段容器样式 */
.code-snippet-container {
    margin-top: 8px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #f8f9fa;
}

.code-snippet-container .source.highlight {
    margin: 0;
    border-radius: 0 0 4px 4px;
}
</style>
{% endblock %}

{% block content %}
<div class="row fade-in-up">
    <div class="col-md-12 mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb bg-transparent p-0">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('index') }}" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>首页
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ url_for('project_detail', project_id=project.id) }}" class="text-decoration-none">
                        <i class="fas fa-project-diagram me-1"></i>{{ project.name }}
                    </a>
                </li>
                <li class="breadcrumb-item active">
                    <i class="fas fa-chart-line me-1"></i>分析报告
                </li>
            </ol>
        </nav>
        
        <div class="text-center mb-4">
            <div class="mb-3">
                <i class="fas fa-chart-line fa-4x pulse" style="background: var(--primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
            </div>
            <h2 class="fw-bold mb-2">测试质量分析报告</h2>
            <p class="lead text-muted">项目 "{{ project.name }}" 的智能化测试质量评估</p>
        </div>
    </div>
</div>

<!-- 总体评分卡片 -->
<div class="row fade-in-up" style="animation-delay: 0.1s;">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-3 text-center">
                        <div class="display-3 fw-bold mb-2" style="background: var(--{{ 'success' if analysis.score >= 80 else ('warning' if analysis.score >= 60 else 'danger') }}); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                            {{ analysis.score }}
                        </div>
                        <div class="badge bg-{{ 'success' if analysis.score >= 80 else ('warning' if analysis.score >= 60 else 'danger') }} fs-6 px-3 py-2">
                            {% if suggestions.grade %}
                                {{ suggestions.grade.level }} - {{ suggestions.grade.description }}
                            {% else %}
                                {{ 'A' if analysis.score >= 80 else ('B' if analysis.score >= 60 else 'C') }} - {{ '优秀' if analysis.score >= 80 else ('良好' if analysis.score >= 60 else '需改进') }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h4 class="mb-3">质量概览</h4>
                        <div class="progress mb-2" style="height: 12px;">
                            <div class="progress-bar bg-{{ 'success' if analysis.score >= 80 else ('warning' if analysis.score >= 60 else 'danger') }}" 
                                 style="width: {{ analysis.score }}%"></div>
                        </div>
                        <div class="d-flex justify-content-between small text-muted">
                            <span>0</span>
                            <span>{{ analysis.score }}/100</span>
                            <span>100</span>
                        </div>
                        
                        <!-- 质量摘要 -->
                        <div class="mt-3 p-3 rounded" style="background: rgba(13, 110, 253, 0.05); border-left: 4px solid #0d6efd;">
                            <h6 class="text-primary mb-2">
                                <i class="fas fa-chart-bar me-1"></i>质量分析摘要
                            </h6>
                            <p class="small mb-0 text-dark">
                                本次分析共评估了 <strong>{{ rule_violations_data.get('analyzed_file_count', test_files|length) }}</strong> 个测试文件，
                                涉及 <strong>{{ total_test_methods }}</strong> 个测试方法，
                                项目整体质量评分为 <strong>{{ analysis.score }}</strong> 分。
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-2 rounded" style="background: rgba(40, 167, 69, 0.1);">
                                    <i class="fas fa-file-alt fa-lg text-success"></i>
                                    <div class="fw-bold">{{ rule_violations_data.get('analyzed_file_count', test_files|length) }}</div>
                                    <div class="small text-muted">测试文件</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-2 rounded" style="background: rgba(13, 110, 253, 0.1);">
                                    <i class="fas fa-code fa-lg text-info"></i>
                                    <div class="fw-bold">{{ total_test_methods }}</div>
                                    <div class="small text-muted">测试方法</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-2 rounded" style="background: rgba(220, 53, 69, 0.1);">
                                    <i class="fas fa-exclamation-triangle fa-lg text-danger"></i>
                                    <div class="fw-bold">{{ total_violations }}</div>
                                    <div class="small text-muted">违规数量</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-2 rounded" style="background: rgba(253, 126, 20, 0.1);">
                                    <i class="fas fa-bug fa-lg text-warning"></i>
                                    <div class="fw-bold">{{ rule_issues|length }}</div>
                                    <div class="small text-muted">问题规则</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if suggestions.quick_summary %}
                <div class="alert alert-info mt-3 mb-0" style="border: none; background: rgba(13, 110, 253, 0.1);">
                    <i class="fas fa-info-circle me-2"></i>
                    {{ suggestions.quick_summary }}
                </div>
                {% elif rule_violations_data.get('analysis_summary', {}).get('overall_assessment') %}
                <div class="alert alert-info mt-3 mb-0" style="border: none; background: rgba(13, 110, 253, 0.1);">
                    <i class="fas fa-info-circle me-2"></i>
                    {{ rule_violations_data.analysis_summary.overall_assessment }}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 规则问题概览 -->
{% if rule_issues %}
<div class="row fade-in-up" style="animation-delay: 0.2s;">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>质量问题分析（按设计原则分类）</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for rule_name, issue_data in rule_issues.items() %}
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-start border-warning border-4">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0 fw-bold">{{ rule_name }}</h6>
                                    <span class="badge bg-{{ 'success' if issue_data.average_score >= 8 else ('warning' if issue_data.average_score >= 6 else 'danger') }}">
                                        {{ issue_data.average_score }}/10
                                    </span>
                                </div>
                                <p class="small text-muted mb-2">发现 {{ issue_data.violation_count }} 个违规</p>
                                
                                {% if issue_data.common_issues %}
                                <div class="mt-2">
                                    <small class="text-muted">常见问题：</small>
                                    <ul class="small mb-0 ps-3">
                                        {% for issue in issue_data.common_issues[:2] %}
                                        <li>{{ issue|truncate(80) }}</li>
                                        {% endfor %}
                                        {% if issue_data.common_issues|length > 2 %}
                                        <li class="text-muted">... 还有 {{ issue_data.common_issues|length - 2 }} 个类似问题</li>
                                        {% endif %}
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 文件级问题摘要 -->
<div class="row fade-in-up" style="animation-delay: 0.3s;">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-file-medical me-2"></i>文件级问题摘要</h5>
            </div>
            <div class="card-body">
                {% if test_files %}
                <div class="file-issues-summary">
                    <p class="mb-3 text-muted">各测试文件存在的主要问题及改进建议：</p>
                    
                    {% for file in test_files %}
                    {% set file_data = file.get_analysis_result() %}
                    {% set detailed_violations = file_data.get('detailed_violations', []) %}
                    {% set all_violations = detailed_violations %}
                    {% set valid_violations = detailed_violations | selectattr("line_number") | selectattr("line_number", "greaterthan", 0) | list %}
                    
                    {% if all_violations %}
                    <div class="file-issue-item mb-3 p-3 rounded border-start border-warning border-4" style="background: rgba(255, 193, 7, 0.05);">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0 fw-bold">
                                <i class="fas fa-file-code me-1 text-primary"></i>{{ file.file_name }}
                            </h6>
                            <span class="badge bg-{{ 'success' if file.score >= 80 else ('warning' if file.score >= 60 else 'danger') }}">
                                {{ file.score }}分
                            </span>
                        </div>
                        
                        <!-- 主要问题统计（基于所有违规） -->
                        {% set violation_groups = {} %}
                        {% for violation in all_violations %}
                            {% set principle = violation.get('principle_name', '未知原则') %}
                            {% if principle not in violation_groups %}
                                {% set _ = violation_groups.update({principle: []}) %}
                            {% endif %}
                            {% set _ = violation_groups[principle].append(violation) %}
                        {% endfor %}
                        
                        {% if violation_groups %}
                        {% set most_common_principle = violation_groups.keys() | list | first %}
                        {% set most_violations = violation_groups[most_common_principle] %}
                        <!-- 找出数量最多的违规类型 -->
                        {% for principle, violations in violation_groups.items() %}
                            {% if violations|length > most_violations|length %}
                                {% set most_common_principle = principle %}
                                {% set most_violations = violations %}
                            {% endif %}
                        {% endfor %}
                        
                        <p class="mb-2">
                            中主要存在「<strong class="text-warning">{{ most_common_principle }}</strong>」问题，
                            {% set first_violation = most_violations | first %}
                            {% if first_violation.suggestions %}
                            建议{{ first_violation.suggestions[0] }}；
                            {% elif most_common_principle == "独立性原则" %}
                            建议确保每个测试方法独立运行，不依赖其他测试的状态；
                            {% elif most_common_principle == "测试粒度" %}
                            建议将复杂的测试方法拆分为更小的独立测试；
                            {% elif most_common_principle == "错误处理" %}
                            建议添加异常情况的测试用例；
                            {% elif most_common_principle == "断言原则" %}
                            建议为每个测试方法添加明确的断言语句；
                            {% elif most_common_principle == "边界值测试" %}
                            建议添加边界条件和极值的测试用例；
                            {% else %}
                            建议详细查看具体代码位置进行改进；
                            {% endif %}
                        </p>
                        {% endif %}
                        
                        <div class="small text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            发现 {{ all_violations|length }} 个问题，
                            其中 {{ valid_violations|length }} 个可精确定位到代码行，
                            涉及 {{ violation_groups|length }} 种违规类型
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h6>未发现文件级问题</h6>
                    <p class="text-muted">所有测试文件质量良好</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 可定位源码的违规类型展示 -->
<div class="row fade-in-up" style="animation-delay: 0.4s;">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h5><i class="fas fa-crosshairs me-2"></i>可定位源码的违规详情</h5>
            </div>
            <div class="card-body">
                <!-- 收集所有可定位的违规 -->
                {% set all_locatable_violations = [] %}
                {% for file in test_files %}
                    {% set file_data = file.get_analysis_result() %}
                    {% set detailed_violations = file_data.get('detailed_violations', []) %}
                    {% for violation in detailed_violations %}
                        {% if violation.get('line_number', 0) > 0 %}
                            {% set enhanced_violation = violation.copy() %}
                            {% set _ = enhanced_violation.update({'file_name': file.file_name, 'file_id': file.id}) %}
                            {% set _ = all_locatable_violations.append(enhanced_violation) %}
                        {% endif %}
                    {% endfor %}
                {% endfor %}
                
                {% if all_locatable_violations %}
                <div class="mb-3">
                    <div class="alert alert-warning border-0" style="background: rgba(255, 193, 7, 0.1);">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        共发现 <strong>{{ all_locatable_violations|length }}</strong> 个可精确定位到源码行的违规，
                        点击"查看源码"可查看具体代码片段。
                    </div>
                </div>
                
                <!-- 按违规类型分组展示 -->
                {% set violation_types = {} %}
                {% for violation in all_locatable_violations %}
                    {% set principle = violation.get('principle_name', '未知原则') %}
                    {% if principle not in violation_types %}
                        {% set _ = violation_types.update({principle: []}) %}
                    {% endif %}
                    {% set _ = violation_types[principle].append(violation) %}
                {% endfor %}
                
                <div class="row">
                    {% for principle, violations in violation_types.items() %}
                    <div class="col-md-12 mb-4">
                        <div class="card border-start border-danger border-4">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 fw-bold text-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i>{{ principle }}
                                    </h6>
                                    <span class="badge bg-danger">{{ violations|length }} 处</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for violation in violations %}
                                    <div class="col-md-6 mb-3">
                                        <div class="border rounded p-3" style="background: rgba(220, 53, 69, 0.05);">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div class="small text-muted">
                                                    <i class="fas fa-file me-1"></i>{{ violation.file_name }}
                                                </div>
                                                <span class="badge bg-info">
                                                    <i class="fas fa-map-marker-alt me-1"></i>行 {{ violation.line_number }}
                                                </span>
                                            </div>
                                            
                                            <p class="small mb-2">{{ violation.description }}</p>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="small text-muted">
                                                    规则：{{ violation.rule_name }}
                                                </div>
                                                <button class="btn btn-sm btn-outline-primary show-code-snippet" 
                                                        data-line="{{ violation.line_number }}" 
                                                        data-file-id="{{ violation.file_id }}">
                                                    <i class="fas fa-code me-1"></i>查看源码
                                                </button>
                                            </div>
                                            
                                            <div id="code-snippet-{{ violation.file_id }}-{{ violation.line_number }}" 
                                                 class="code-snippet-container mt-2" style="display: none;"></div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h6>未发现可定位的违规</h6>
                    <p class="text-muted">所有问题都是整体性问题，无法定位到具体代码行</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 分析时间信息 -->
<div class="row fade-in-up" style="animation-delay: 0.4s;">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-body p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted small">
                        <i class="fas fa-clock me-1"></i>
                        分析时间：{{ analysis.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    </div>
                    <div class="text-muted small">
                        <i class="fas fa-brain me-1"></i>
                        由智谱AI GLM大语言模型提供技术支持
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析结果数据（用于JavaScript读取） -->
<div id="analysisResult" data-analysis-result='{{ {
    "score": analysis.score,
    "grade": suggestions.get("grade", {}),
    "method_count": test_files|length,
    "violations": rule_issues,
    "suggestions": suggestions
}|tojson }}'></div>

{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后的动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 数字计数动画
    const scoreElement = document.querySelector('.display-3');
    if (scoreElement) {
        const targetScore = {{ analysis.score }};
        animateCounter(scoreElement, targetScore, 2000);
    }
    
    // 进度条动画
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        setTimeout(() => {
            progressBar.style.transition = 'width 1.5s ease-in-out';
            progressBar.style.width = '{{ analysis.score }}%';
        }, 500);
    }
    
    // 源代码查看功能
    const showCodeButtons = document.querySelectorAll('.show-code-snippet');
    
    showCodeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const lineNumber = this.getAttribute('data-line');
            const fileId = this.getAttribute('data-file-id');
            const snippetContainer = document.getElementById('code-snippet-' + fileId + '-' + lineNumber);
            
            if (snippetContainer.style.display === 'none') {
                // 显示代码片段
                snippetContainer.style.display = 'block';
                this.innerHTML = '<i class="fas fa-eye-slash me-1"></i>隐藏源码';
                
                // 如果还没有加载代码，则加载代码
                if (!snippetContainer.innerHTML.trim()) {
                    fetch(`/api/files/${fileId}/code_snippet?line=${lineNumber}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.code) {
                                let headerContent = `
                                    <i class="fas fa-file-code me-1"></i>
                                    显示第 ${data.start_line}-${data.end_line} 行，共 ${data.total_lines} 行
                                `;
                                
                                if (data.note) {
                                    // 行号超出范围的情况
                                    headerContent += `
                                        <span class="float-end text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>${data.note}
                                        </span>
                                    `;
                                } else {
                                    // 正常情况，显示问题行
                                    headerContent += `
                                        <span class="float-end">
                                            <i class="fas fa-crosshairs me-1"></i>问题行：第 ${data.highlight_line} 行
                                        </span>
                                    `;
                                }
                                
                                snippetContainer.innerHTML = `
                                    <div class="code-snippet-header bg-secondary text-white px-3 py-2 small">
                                        ${headerContent}
                                    </div>
                                    <div class="code-content">
                                        ${data.code}
                                    </div>
                                `;
                            } else {
                                snippetContainer.innerHTML = '<div class="alert alert-warning">无法加载代码片段</div>';
                            }
                        })
                        .catch(error => {
                            snippetContainer.innerHTML = '<div class="alert alert-danger">加载代码片段时出错</div>';
                        });
                }
            } else {
                // 隐藏代码片段
                snippetContainer.style.display = 'none';
                this.innerHTML = '<i class="fas fa-code me-1"></i>查看源码';
            }
        });
    });
});
</script>
{% endblock %}
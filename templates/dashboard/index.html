{% extends 'layout/base.html' %}

{% block title %}首页 - Java单元测试质量分析器{% endblock %}

{% block content %}
<div class="row fade-in-up">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-project-diagram me-2"></i>项目概览</h4>
            </div>
            <div class="card-body">
                <div class="text-center mb-5">
                    <div class="mb-4">
                        <i class="fas fa-vial fa-4x pulse" style="background: var(--primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                    </div>
                    <h2 class="fw-bold mb-3">欢迎使用Java单元测试质量分析器</h2>
                    <p class="lead text-muted">基于10个核心原则评估Java单元测试的质量和有效性</p>
                    <div class="d-flex justify-content-center mt-4">
                        <div class="badge bg-success me-2">智能分析</div>
                        <div class="badge bg-info me-2">质量评估</div>
                        <div class="badge bg-warning">改进建议</div>
                    </div>
                </div>
                
                {% if projects %}
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="mb-4 fw-bold"><i class="fas fa-folder-open me-2"></i>我的项目</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th><i class="fas fa-project-diagram me-1"></i>项目名称</th>
                                            <th><i class="fas fa-align-left me-1"></i>描述</th>
                                            <th><i class="fas fa-calendar me-1"></i>创建时间</th>
                                            <th><i class="fas fa-chart-line me-1"></i>分析次数</th>
                                            <th><i class="fas fa-star me-1"></i>最新得分</th>
                                            <th><i class="fas fa-cogs me-1"></i>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for project in projects %}
                                            <tr class="fade-in-up" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                                                <td>
                                                    <a href="{{ url_for('project_detail', project_id=project.id) }}" 
                                                       class="fw-bold text-decoration-none">
                                                        <i class="fas fa-cube me-2"></i>{{ project.name }}
                                                    </a>
                                                </td>
                                                <td class="text-muted">{{ project.description }}</td>
                                                <td>
                                                    <span class="badge bg-light text-dark">
                                                        <i class="fas fa-clock me-1"></i>{{ project.created_at.strftime('%Y-%m-%d %H:%M') }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">
                                                        {{ project.analyses|length }} 次
                                                    </span>
                                                </td>
                                                <td>
                                                    {% if project.analyses %}
                                                        <span class="badge bg-{{ 'success' if project.analyses[0].score >= 80 else ('warning' if project.analyses[0].score >= 60 else 'danger') }} pulse" 
                                                              style="font-size: 0.9rem; padding: 0.5rem 0.75rem;">
                                                            <i class="fas fa-trophy me-1"></i>{{ project.analyses[0].score }} 分
                                                        </span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">
                                                            <i class="fas fa-question me-1"></i>待分析
                                                        </span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ url_for('project_detail', project_id=project.id) }}" 
                                                           class="btn btn-sm btn-info me-1">
                                                            <i class="fas fa-eye me-1"></i>详情
                                                        </a>
                                                        <a href="{{ url_for('upload_files', project_id=project.id) }}" 
                                                           class="btn btn-sm btn-success">
                                                            <i class="fas fa-upload me-1"></i>上传
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5 fade-in-up">
                        <div class="mb-4">
                            <i class="fas fa-folder-open fa-5x pulse" style="background: var(--info); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h3 class="fw-bold mb-3">还没有项目</h3>
                        <p class="lead text-muted mb-4">创建一个新项目开始分析Java单元测试质量</p>
                        <a href="{{ url_for('new_project') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus-circle me-2"></i>创建新项目
                        </a>
                        <div class="mt-4">
                            <small class="text-muted">
                                <i class="fas fa-lightbulb me-1"></i>提示：上传您的Java项目代码，获得智能化的测试质量分析报告
                            </small>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-5 fade-in-up" style="animation-delay: 0.3s;">
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-lightbulb me-2"></i>关于测试质量分析</h5>
            </div>
            <div class="card-body">
                <p class="lead mb-4">本工具基于以下<strong>10个核心原则</strong>评估Java单元测试质量：</p>
                <div class="row">
                    <div class="col-12">
                        <div class="mb-3 p-3 rounded" style="background: rgba(102, 126, 234, 0.05);">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-primary me-2">1</span>
                                <strong>独立性原则</strong>
                            </div>
                            <small class="text-muted">测试应该是独立的，不依赖于其他测试</small>
                        </div>
                        <div class="mb-3 p-3 rounded" style="background: rgba(79, 172, 254, 0.05);">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-info me-2">2</span>
                                <strong>自动化原则</strong>
                            </div>
                            <small class="text-muted">测试应能自动运行，无需人工干预</small>
                        </div>
                        <div class="mb-3 p-3 rounded" style="background: rgba(34, 193, 195, 0.05);">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-success me-2">3-5</span>
                                <strong>测试完整性</strong>
                            </div>
                            <small class="text-muted">测试粒度、边界值测试、正确输入处理</small>
                        </div>
                        <div class="mb-3 p-3 rounded" style="background: rgba(253, 187, 45, 0.05);">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-warning me-2">6-8</span>
                                <strong>规范性原则</strong>
                            </div>
                            <small class="text-muted">错误处理、目录结构、命名规范</small>
                        </div>
                        <div class="mb-3 p-3 rounded" style="background: rgba(247, 112, 154, 0.05);">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-danger me-2">9-10</span>
                                <strong>质量保证</strong>
                            </div>
                            <small class="text-muted">断言原则、核心代码覆盖</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card shadow h-100">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-magic me-2"></i>智谱AI赋能</h5>
            </div>
            <div class="card-body d-flex flex-column">
                <p class="lead mb-4">本工具利用<strong>智谱AI的GLM大语言模型</strong>进行深度代码分析</p>
                <div class="row mb-4 flex-grow-1">
                    <div class="col-12">
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <i class="fas fa-brain fa-2x" style="color: #4facfe;"></i>
                            </div>
                            <div>
                                <strong>语义理解</strong>
                                <div class="small text-muted">理解Java代码的语义和上下文关系</div>
                            </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <i class="fas fa-search fa-2x" style="color: #00f2fe;"></i>
                            </div>
                            <div>
                                <strong>问题识别</strong>
                                <div class="small text-muted">识别测试代码中的潜在问题和优化空间</div>
                            </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <i class="fas fa-lightbulb fa-2x" style="color: #fdbb2d;"></i>
                            </div>
                            <div>
                                <strong>改进建议</strong>
                                <div class="small text-muted">提供针对性的改进建议和代码示例</div>
                            </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <i class="fas fa-chart-pie fa-2x" style="color: #fa709a;"></i>
                            </div>
                            <div>
                                <strong>质量评估</strong>
                                <div class="small text-muted">分析测试覆盖率和最佳实践符合度</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-auto">
                    <a href="{{ url_for('new_project') }}" class="btn btn-success btn-lg pulse">
                        <i class="fas fa-bolt me-2"></i>立即开始分析
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 特性展示区域 -->
<div class="row mt-5 fade-in-up" style="animation-delay: 0.6s;">
    <div class="col-12">
        <div class="text-center mb-5">
            <h3 class="fw-bold">为什么选择我们？</h3>
            <p class="lead text-muted">专业的Java测试质量分析，助力代码质量提升</p>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card shadow h-100 text-center">
            <div class="card-body">
                <div class="mb-3">
                    <i class="fas fa-rocket fa-3x" style="background: var(--success); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                </div>
                <h5 class="fw-bold">快速分析</h5>
                <p class="text-muted">秒级完成代码分析，快速获得测试质量报告</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card shadow h-100 text-center">
            <div class="card-body">
                <div class="mb-3">
                    <i class="fas fa-shield-alt fa-3x" style="background: var(--info); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                </div>
                <h5 class="fw-bold">专业准确</h5>
                <p class="text-muted">基于行业最佳实践，提供专业的质量评估</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card shadow h-100 text-center">
            <div class="card-body">
                <div class="mb-3">
                    <i class="fas fa-cogs fa-3x" style="background: var(--warning); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                </div>
                <h5 class="fw-bold">智能建议</h5>
                <p class="text-muted">AI驱动的改进建议，让测试代码更优秀</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

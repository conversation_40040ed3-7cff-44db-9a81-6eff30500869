{% extends 'layout/base.html' %}

{% block title %}{{ project.name }} - 项目详情{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item active">{{ project.name }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4><i class="fas fa-project-diagram"></i> {{ project.name }}</h4>
                <a href="{{ url_for('upload_files', project_id=project.id) }}" class="btn btn-light">
                    <i class="fas fa-upload"></i> 上传文件
                </a>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h5>项目描述</h5>
                    <p>{{ project.description or '无描述' }}</p>
                    <p><small class="text-muted">创建于: {{ project.created_at.strftime('%Y-%m-%d %H:%M') }}</small></p>
                </div>
                
                {% if analyses %}
                    <h5 class="mb-3">分析历史</h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th><input type="checkbox" id="selectAllAnalyses"></th>
                                    <th>分析ID</th>
                                    <th>创建时间</th>
                                    <th>总分</th>
                                    <th>测试文件数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for analysis in analyses %}
                                    <tr>
                                        <td><input type="checkbox" class="analysis-checkbox" value="{{ analysis.id }}"></td>
                                        <td>#{{ analysis.id }}</td>
                                        <td>{{ analysis.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1 me-2" style="height: 10px;">
                                                    <div class="progress-bar bg-{{ 'success' if analysis.score >= 80 else ('warning' if analysis.score >= 60 else 'danger') }}" 
                                                         role="progressbar" 
                                                         style="width: {{ analysis.score }}%;" 
                                                         aria-valuenow="{{ analysis.score }}" 
                                                         aria-valuemin="0" 
                                                         aria-valuemax="100"></div>
                                                </div>
                                                <span class="badge bg-{{ 'success' if analysis.score >= 80 else ('warning' if analysis.score >= 60 else 'danger') }}">
                                                    {{ analysis.score }}
                                                </span>
                                            </div>
                                        </td>
                                        <td>{{ analysis.test_files|length }}</td>
                                        <td>
                                            <a href="{{ url_for('analysis_detail', analysis_id=analysis.id) }}" class="btn btn-sm btn-info me-2">
                                                <i class="fas fa-chart-pie"></i> 详情
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger delete-single-analysis" data-id="{{ analysis.id }}">
                                                <i class="fas fa-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3 d-flex justify-content-end">
                        <button type="button" id="batchDeleteBtn" class="btn btn-danger" disabled>
                            <i class="fas fa-trash"></i> 批量删除选中项
                        </button>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                        <h5>还没有分析记录</h5>
                        <p class="text-muted">点击下方按钮上传文件进行分析</p>
                        <a href="{{ url_for('upload_files', project_id=project.id) }}" class="btn btn-success">
                            <i class="fas fa-upload"></i> 上传文件
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if analyses %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-chart-line"></i> 分析趋势</h5>
            </div>
            <div class="card-body">
                <canvas id="scoreChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if analyses %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 准备数据
        var dates = [{% for analysis in analyses %}'{{ analysis.created_at.strftime("%Y-%m-%d") }}'{% if not loop.last %}, {% endif %}{% endfor %}];
        var scores = [{% for analysis in analyses %}{{ analysis.score }}{% if not loop.last %}, {% endif %}{% endfor %}];
        
        // 创建图表
        var ctx = document.getElementById('scoreChart').getContext('2d');
        var chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates.reverse(),  // 按时间顺序排列
                datasets: [{
                    label: '质量得分',
                    data: scores.reverse(),
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: '分数'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '日期'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return '得分: ' + context.raw;
                            }
                        }
                    }
                }
            }
        });

        // JavaScript for delete functionality
        const selectAllCheckbox = document.getElementById('selectAllAnalyses');
        const analysisCheckboxes = document.querySelectorAll('.analysis-checkbox');
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');

        function updateBatchDeleteButton() {
            const checkedCount = document.querySelectorAll('.analysis-checkbox:checked').length;
            batchDeleteBtn.disabled = checkedCount === 0;
        }

        selectAllCheckbox.addEventListener('change', function() {
            analysisCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBatchDeleteButton();
        });

        analysisCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (!this.checked) {
                    selectAllCheckbox.checked = false;
                }
                updateBatchDeleteButton();
            });
        });

        batchDeleteBtn.addEventListener('click', function() {
            const selectedIds = Array.from(document.querySelectorAll('.analysis-checkbox:checked'))
                                 .map(checkbox => checkbox.value);
            if (selectedIds.length > 0 && confirm('确定要删除选中的所有分析记录吗？此操作不可撤销。')) {
                fetch('/api/analyses/batch_delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ analysis_ids: selectedIds })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('选中分析记录删除成功！');
                        location.reload(); // 刷新页面
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除过程中发生错误。');
                });
            }
        });

        document.querySelectorAll('.delete-single-analysis').forEach(button => {
            button.addEventListener('click', function() {
                const analysisId = this.dataset.id;
                if (confirm(`确定要删除分析记录 #${analysisId} 吗？此操作不可撤销。`)) {
                    fetch(`/api/analyses/${analysisId}/delete`, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(`分析记录 #${analysisId} 删除成功！`);
                            location.reload(); // 刷新页面
                        } else {
                            alert('删除失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('删除过程中发生错误。');
                    });
                }
            });
        });

        updateBatchDeleteButton(); // Initial state
    });
</script>
{% endif %}
{% endblock %} 
{% extends 'layout/base.html' %}

{% block title %}上传文件 - {{ project.name }}{% endblock %}

{% block content %}
<div class="row fade-in-up">
    <div class="col-md-12 mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb bg-transparent p-0">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('index') }}" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>首页
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ url_for('project_detail', project_id=project.id) }}" class="text-decoration-none">
                        <i class="fas fa-project-diagram me-1"></i>{{ project.name }}
                    </a>
                </li>
                <li class="breadcrumb-item active">
                    <i class="fas fa-upload me-1"></i>上传文件
                </li>
            </ol>
        </nav>
        
        <div class="text-center mb-4">
            <div class="mb-3">
                <i class="fas fa-cloud-upload-alt fa-4x pulse" style="background: var(--primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
            </div>
            <h2 class="fw-bold mb-2">上传项目文件</h2>
            <p class="lead text-muted">为 "{{ project.name }}" 项目上传Java代码文件</p>
        </div>
    </div>
</div>

<div class="row fade-in-up" style="animation-delay: 0.2s;">
    <div class="col-md-10 offset-md-1">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-cloud-upload-alt me-2"></i>文件上传区域</h4>
            </div>
            <div class="card-body p-4">
                <form method="POST" enctype="multipart/form-data" action="{{ url_for('upload_files', project_id=project.id) }}" id="uploadForm">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-4">
                        <div class="upload-area text-center py-5 rounded" style="border: 2px dashed rgba(102, 126, 234, 0.3); background: rgba(102, 126, 234, 0.05); transition: all 0.3s ease;">
                            <div class="upload-icon mb-3">
                                <i class="fas fa-file-code fa-4x" style="color: #667eea;"></i>
                            </div>
                            <h4 class="mb-3">拖放文件到此处或点击选择</h4>
                            <p class="text-muted mb-4">支持Java源文件、测试文件或完整项目ZIP压缩包</p>
                            
                            <div class="mb-4">
                                <div class="row justify-content-center">
                                    <div class="col-auto">
                                        <div class="badge bg-success me-2">
                                            <i class="fas fa-file-code me-1"></i>.java
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="badge bg-info me-2">
                                            <i class="fas fa-file-archive me-1"></i>.zip
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="badge bg-warning">
                                            <i class="fas fa-folder me-1"></i>多文件
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                {{ form.file(class="form-control form-control-lg", style="max-width: 500px; margin: 0 auto;", accept=".java,.zip", multiple=true) }}
                            </div>
                            
                            {% if form.file.errors %}
                                <div class="alert alert-danger mt-3">
                                    {% for error in form.file.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            
                            <div class="upload-progress mt-3" style="display: none;">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
                                </div>
                                <p class="mt-2 text-muted">正在上传文件...</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-lg", style="height: 60px; font-size: 1.1rem;", id="submitBtn") }}
                    </div>
                </form>
                
                <script>
                // 添加表单提交时的进度显示
                document.getElementById('uploadForm').addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // 验证文件是否已选择
                    const fileInput = this.querySelector('input[type="file"]');
                    if (!fileInput.files || fileInput.files.length === 0) {
                        createNotification('提示', '请先选择要上传的文件', 'warning');
                        return;
                    }
                    
                    // 显示分析进度
                    const progress = showAnalysisProgress();
                    
                    // 模拟分析进度更新
                    let currentProgress = 0;
                    const progressSteps = [
                        {percent: 10, message: '上传文件中...', detail: '正在将文件传输到服务器'},
                        {percent: 25, message: '解析代码结构...', detail: '识别Java文件和测试文件'},
                        {percent: 40, message: '执行静态分析...', detail: '分析代码结构和语法'},
                        {percent: 60, message: 'AI深度分析...', detail: '智谱AI正在理解代码语义'},
                        {percent: 80, message: '生成质量报告...', detail: '汇总分析结果和建议'},
                        {percent: 95, message: '完善分析结果...', detail: '准备展示分析报告'},
                        {percent: 100, message: '分析完成', detail: '即将跳转到结果页面'}
                    ];
                    
                    let stepIndex = 0;
                    const updateInterval = setInterval(() => {
                        if (stepIndex < progressSteps.length) {
                            const step = progressSteps[stepIndex];
                            progress.updateProgress(step.message, step.detail, step.percent);
                            stepIndex++;
                        } else {
                            clearInterval(updateInterval);
                        }
                    }, 1500); // 每1.5秒更新一次
                    
                    // 实际提交表单
                    const formData = new FormData(this);
                    
                    // 正确提取项目ID：从 /projects/123/upload 中提取 123
                    const actionParts = this.action.split('/');
                    const projectIndex = actionParts.indexOf('projects');
                    const projectId = actionParts[projectIndex + 1];
                    
                    if (!projectId || isNaN(projectId)) {
                        throw new Error('无法获取项目ID，请刷新页面重试');
                    }
                    
                    // 首先尝试使用API端点，如果失败则使用原始表单提交
                    const apiUrl = `/api/projects/${projectId}/upload_progress`;
                    console.log('尝试API URL:', apiUrl);
                    
                    fetch(apiUrl, {
                        method: 'POST',
                        body: formData
                    })
                    .catch(apiError => {
                        console.log('API调用失败，使用原始表单提交:', apiError);
                        // 如果API失败，回退到原始表单提交
                        return fetch(this.action, {
                            method: 'POST',
                            body: formData
                        });
                    })
                    .then(response => {
                        if (response.ok) {
                            // 检查响应类型
                            const contentType = response.headers.get('content-type');
                            if (contentType && contentType.includes('application/json')) {
                                return response.json();
                            } else {
                                // HTML响应，说明是原始表单提交
                                return response.text().then(html => ({ isHtml: true, html: html }));
                            }
                        }
                        throw new Error('上传失败');
                    })
                    .then(data => {
                        // 清除进度更新
                        clearInterval(updateInterval);
                        
                        if (data.isHtml) {
                            // 处理HTML响应（原始表单提交）
                            progress.updateProgress('分析完成', '即将跳转到结果页面', 100);
                            
                            setTimeout(() => {
                                progress.close();
                                
                                // 检查HTML中是否包含成功信息
                                if (data.html.includes('分析成功') || data.html.includes('分析结果')) {
                                    showAnalysisComplete({
                                        score: 75,
                                        grade: { level: 'B', description: '良好' },
                                        method_count: 0,
                                        violations: {},
                                        suggestions: { overall_advice: '分析完成，请查看详细报告' }
                                    });
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 2000);
                                } else {
                                    // 替换页面内容
                                    document.body.innerHTML = data.html;
                                }
                            }, 1000);
                        } else if (data.success) {
                            // 处理JSON API响应
                            progress.updateProgress('分析完成', '即将跳转到结果页面', 100);
                            
                            setTimeout(() => {
                                progress.close();
                                
                                // 显示分析完成弹窗
                                showAnalysisComplete(data.result);
                                
                                // 2秒后跳转到分析详情页
                                setTimeout(() => {
                                    window.location.href = `/analyses/${data.analysis_id}`;
                                }, 2000);
                            }, 1000);
                        } else {
                            throw new Error(data.error || '分析失败');
                        }
                    })
                    .catch(error => {
                        clearInterval(updateInterval);
                        progress.close();
                        createNotification('错误', '文件上传失败: ' + error.message, 'error');
                    });
                });
                </script>
            </div>
        </div>
        
        
        <!-- 上传指南 -->
        <div class="row mt-5 fade-in-up" style="animation-delay: 0.4s;">
            <div class="col-md-6">
                <div class="card h-100 shadow">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-upload me-2"></i>上传指南</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">推荐上传格式：</h6>
                            <div class="d-flex mb-3 align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-file-archive fa-lg" style="color: #667eea;"></i>
                                </div>
                                <div>
                                    <strong>ZIP压缩包</strong>
                                    <div class="small text-muted">包含完整项目结构，分析更全面</div>
                                </div>
                            </div>
                            <div class="d-flex mb-3 align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-file-code fa-lg" style="color: #4facfe;"></i>
                                </div>
                                <div>
                                    <strong>Java源文件</strong>
                                    <div class="small text-muted">单个或多个.java文件</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-light border-start border-primary border-4">
                            <i class="fas fa-folder-tree me-2 text-primary"></i>
                            <strong>目录结构：</strong> 遵循Maven/Gradle标准，便于识别源码和测试关系
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card h-100 shadow">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-cogs me-2"></i>分析流程</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <span class="badge bg-primary rounded-pill">1</span>
                            </div>
                            <div>
                                <strong>文件解析</strong>
                                <div class="small text-muted">自动提取Java源代码和测试代码</div>
                            </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <span class="badge bg-info rounded-pill">2</span>
                            </div>
                            <div>
                                <strong>静态分析</strong>
                                <div class="small text-muted">基于10条核心原则进行代码分析</div>
                            </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <span class="badge bg-warning rounded-pill">3</span>
                            </div>
                            <div>
                                <strong>AI分析</strong>
                                <div class="small text-muted">智谱AI深度语义理解和问题识别</div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <span class="badge bg-success rounded-pill">4</span>
                            </div>
                            <div>
                                <strong>生成报告</strong>
                                <div class="small text-muted">详细分析结果和改进建议</div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-4">
                            <i class="fas fa-clock me-2"></i>
                            <strong>温馨提示：</strong> 分析过程需舐1-3分钟，请耐心等待
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends 'layout/base.html' %}

{% block title %}新建项目 - Java单元测试质量分析器{% endblock %}

{% block content %}
<div class="row fade-in-up">
    <div class="col-md-8 offset-md-2">
        <div class="text-center mb-5">
            <div class="mb-4">
                <i class="fas fa-plus-circle fa-4x pulse" style="background: var(--primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
            </div>
            <h2 class="fw-bold mb-3">创建新项目</h2>
            <p class="lead text-muted">开始您的Java单元测试质量分析之旅</p>
        </div>
        
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-project-diagram me-2"></i>项目信息</h4>
            </div>
            <div class="card-body p-4">
                <form method="POST" action="{{ url_for('new_project') }}" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-4">
                        <label for="name" class="form-label d-flex align-items-center">
                            <i class="fas fa-tag me-2 text-primary"></i>项目名称
                        </label>
                        {{ form.name(class="form-control form-control-lg", placeholder="例如：用户管理系统", required=true) }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                    <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            <i class="fas fa-lightbulb me-1"></i>请输入有意义的项目名称，便于管理和查找
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="description" class="form-label d-flex align-items-center">
                            <i class="fas fa-align-left me-2 text-primary"></i>项目描述
                            <span class="badge bg-secondary ms-2">可选</span>
                        </label>
                        {{ form.description(class="form-control", rows=5, placeholder="请描述您的项目背景、目标或特殊要求...") }}
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>详细的描述有助于更好地理解项目背景
                        </div>
                    </div>
                    
                    <div class="d-grid gap-3">
                        <div class="text-center mb-3">
                            <div class="row">
                                <div class="col-4">
                                    <div class="p-3 rounded" style="background: rgba(102, 126, 234, 0.1);">
                                        <i class="fas fa-upload fa-2x text-primary mb-2"></i>
                                        <div class="small fw-bold">上传代码</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="p-3 rounded" style="background: rgba(79, 172, 254, 0.1);">
                                        <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                        <div class="small fw-bold">智能分析</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="p-3 rounded" style="background: rgba(34, 193, 195, 0.1);">
                                        <i class="fas fa-lightbulb fa-2x text-success mb-2"></i>
                                        <div class="small fw-bold">获得建议</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ form.submit(class="btn btn-primary btn-lg", style="height: 60px; font-size: 1.1rem;") }}
                    </div>
                </form>
            </div>
        </div>
        
        <div class="row mt-5 fade-in-up" style="animation-delay: 0.3s;">
            <div class="col-md-6">
                <div class="card h-100 shadow">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-rocket me-2"></i>创建后可以做什么</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <i class="fas fa-upload fa-lg" style="color: #667eea;"></i>
                            </div>
                            <div>
                                <strong>上传代码文件</strong>
                                <div class="small text-muted">支持Java源代码和测试文件</div>
                            </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <i class="fas fa-file-archive fa-lg" style="color: #4facfe;"></i>
                            </div>
                            <div>
                                <strong>上传ZIP压缩包</strong>
                                <div class="small text-muted">整个项目一键上传</div>
                            </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                            <div class="me-3">
                                <i class="fas fa-chart-bar fa-lg" style="color: #00f2fe;"></i>
                            </div>
                            <div>
                                <strong>查看分析结果</strong>
                                <div class="small text-muted">详细的测试质量报告</div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-trending-up fa-lg" style="color: #fdbb2d;"></i>
                            </div>
                            <div>
                                <strong>跟踪改进过程</strong>
                                <div class="small text-muted">监控质量提升趋势</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100 shadow">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-lightbulb me-2"></i>使用建议</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-light border-start border-primary border-4">
                            <i class="fas fa-tag me-2 text-primary"></i>
                            <strong>命名建议：</strong> 使用有意义的项目名称，便于管理和查找
                        </div>
                        <div class="alert alert-light border-start border-info border-4">
                            <i class="fas fa-code-branch me-2 text-info"></i>
                            <strong>项目结构：</strong> 推荐Maven或Gradle标准目录结构
                        </div>
                        <div class="alert alert-light border-start border-success border-4">
                            <i class="fas fa-vial me-2 text-success"></i>
                            <strong>测试覆盖：</strong> 确保包含充足的单元测试代码
                        </div>
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                遵循这些建议可获得更准确的分析结果
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
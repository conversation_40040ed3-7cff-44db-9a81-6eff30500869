#!/usr/bin/env python3
"""测试LLM调用是否正确返回字典格式"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from services.model_factory import ModelFactory
    
    print("=== 测试LLM调用返回格式 ===")
    
    # 创建模型工厂实例
    factory = ModelFactory()
    
    # 获取OpenRouter服务
    service = factory.get_model_service("openrouter")
    print(f"服务实例: {service}")
    print(f"API Key配置: {bool(service.api_key)}")
    print(f"服务可用性: {service.is_available()}")
    
    # 测试分析方法调用
    test_prompt = """
    分析以下Java单元测试是否覆盖了边界值。
    
    测试代码：
    ```java
    @Test
    public void testCalculate() {
        assertEquals(0, calculator.add(0, 0));
        assertEquals(5, calculator.add(2, 3));
    }
    ```
    
    请以JSON格式回答，包含以下字段：
    1. is_compliant: boolean，表示是否覆盖边界值
    2. compliance_score: number，符合程度的分数（0-10）
    3. explanation: string，详细解释
    4. issues: array，具体问题列表
    5. suggestions: array，改进建议列表
    """
    
    print(f"\n测试提示词: {test_prompt[:100]}...")
    
    try:
        result = service._call_llm(test_prompt)
        print(f"调用结果类型: {type(result)}")
        
        if isinstance(result, dict):
            print("✅ 返回格式正确 - 字典类型")
            print(f"字典键: {list(result.keys())}")
            
            # 检查是否包含错误
            if "error" in result:
                print(f"❌ 调用失败: {result['error']}")
            else:
                print("✅ 调用成功!")
                # 检查必要的字段
                required_fields = ['is_compliant', 'compliance_score', 'explanation']
                for field in required_fields:
                    if field in result:
                        print(f"  ✅ {field}: {result[field]}")
                    else:
                        print(f"  ⚠️  缺少字段 {field}")
        else:
            print(f"❌ 返回格式错误 - 期望字典，得到 {type(result)}")
            print(f"返回内容: {result}")
            
    except Exception as e:
        print(f"❌ 调用异常: {e}")
        import traceback
        traceback.print_exc()

except Exception as e:
    print(f"导入模块失败: {e}")
    import traceback
    traceback.print_exc()
